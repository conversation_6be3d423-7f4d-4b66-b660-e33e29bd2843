#!/usr/bin/env python3
"""
Test runner for the Crawl4AI Agent.

This script runs all tests and provides a comprehensive test report.
It can run different test suites based on command line arguments.
"""

import sys
import subprocess
import argparse
import time
import os
from pathlib import Path


def run_command(cmd, description):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    print(f"Command: {' '.join(cmd)}")
    print()

    # Set environment variable to suppress external dependency warnings
    env = os.environ.copy()
    env['PYTHONWARNINGS'] = 'ignore::DeprecationWarning'

    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    end_time = time.time()

    print(result.stdout)
    if result.stderr:
        print("STDERR:")
        print(result.stderr)

    duration = end_time - start_time
    status = "✅ PASSED" if result.returncode == 0 else "❌ FAILED"
    print(f"\n{status} - Duration: {duration:.2f}s")

    return result.returncode == 0


def run_unit_tests():
    """Run unit tests for crawler functions."""
    return run_command(
        ["python", "-m", "pytest", "tests/crawler/",
            "tests/utils/", "-v", "--tb=short"],
        "Unit Tests - Crawler and Utility Functions"
    )


def run_tool_tests():
    """Run tests for LangGraph tools."""
    return run_command(
        ["python", "-m", "pytest",
            "tests/agent/test_langgraph_tools.py", "-v", "--tb=short"],
        "Tool Tests - LangGraph Tools"
    )


def run_integration_tests():
    """Run integration tests for complete workflows."""
    return run_command(
        ["python", "-m", "pytest", "tests/agent/test_integration.py", "-v", "--tb=short"],
        "Integration Tests - Complete Workflows"
    )


def run_agent_tests():
    """Run all agent-related tests."""
    return run_command(
        ["python", "-m", "pytest", "tests/agent/", "-v", "--tb=short"],
        "Agent Tests - LangGraph Workflow and Tools"
    )


def run_all_tests():
    """Run all tests."""
    return run_command(
        ["python", "-m", "pytest", "tests/", "-v", "--tb=short"],
        "All Tests - Complete Test Suite"
    )


def run_quick_tests():
    """Run a quick subset of tests."""
    return run_command(
        ["python", "-m", "pytest", "tests/crawler/test_crawler.py::TestCrawlWebsite::test_crawl_website_success",
         "tests/agent/test_integration.py::TestSingleURLWorkflow::test_summarize_single_url", "-v"],
        "Quick Tests - Basic Functionality"
    )


def run_coverage_tests():
    """Run tests with coverage report."""
    return run_command(
        ["python", "-m", "pytest", "tests/", "--cov=crawler", "--cov=agent",
         "--cov-report=html", "--cov-report=term", "-v"],
        "Coverage Tests - With Coverage Report"
    )


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="Run tests for Crawl4AI Agent")
    parser.add_argument(
        "test_type",
        nargs="?",
        default="all",
        choices=["unit", "tools", "integration",
                 "agent", "all", "quick", "coverage"],
        help="Type of tests to run (default: all)"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )

    args = parser.parse_args()

    print("🚀 Crawl4AI Agent Test Runner")
    print("=" * 60)
    print(f"Test Type: {args.test_type}")
    print(f"Verbose: {args.verbose}")

    # Check if pytest is available
    try:
        subprocess.run(["python", "-m", "pytest", "--version"],
                       capture_output=True, check=True)
    except subprocess.CalledProcessError:
        print("❌ Error: pytest is not installed.")
        print("Install it with: pip install pytest pytest-asyncio pytest-cov")
        return 1

    # Run the appropriate tests
    success = True

    if args.test_type == "unit":
        success = run_unit_tests()
    elif args.test_type == "tools":
        success = run_tool_tests()
    elif args.test_type == "integration":
        success = run_integration_tests()
    elif args.test_type == "agent":
        success = run_agent_tests()
    elif args.test_type == "quick":
        success = run_quick_tests()
    elif args.test_type == "coverage":
        success = run_coverage_tests()
    elif args.test_type == "all":
        print("\n🎯 Running Complete Test Suite")
        print("This will run all tests in sequence...")

        results = []
        results.append(("Unit Tests", run_unit_tests()))
        results.append(("Tool Tests", run_tool_tests()))
        results.append(("Integration Tests", run_integration_tests()))
        results.append(("Agent Tests", run_agent_tests()))

        # Summary
        print(f"\n{'='*60}")
        print("📊 TEST SUMMARY")
        print(f"{'='*60}")

        all_passed = True
        for test_name, passed in results:
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"{test_name}: {status}")
            if not passed:
                all_passed = False

        success = all_passed

        if all_passed:
            print("\n🎉 All tests passed!")
        else:
            print("\n⚠️  Some tests failed. Check the output above for details.")

    # Final result
    print(f"\n{'='*60}")
    if success:
        print("🎉 TEST RUN COMPLETED SUCCESSFULLY")
        return_code = 0
    else:
        print("❌ TEST RUN FAILED")
        return_code = 1

    print(f"{'='*60}")

    return return_code


if __name__ == "__main__":
    sys.exit(main())
