"""
Warnings filter configuration for suppressing external dependency warnings.

This module provides centralized warning suppression for known issues
in external dependencies that we cannot fix directly.
"""

import warnings
from typing import List, Tuple


def suppress_external_warnings() -> None:
    """
    Suppress known warnings from external dependencies.
    
    This function filters out deprecation warnings and other warnings
    from external libraries that we cannot control or fix.
    """
    
    # Pydantic deprecation warnings from external dependencies
    warnings.filterwarnings(
        "ignore",
        message="Support for class-based `config` is deprecated.*",
        category=DeprecationWarning,
        module="pydantic.*"
    )
    
    # fake_http_header importlib deprecation warnings
    warnings.filterwarnings(
        "ignore",
        message="read_text is deprecated.*",
        category=DeprecationWarning,
        module="fake_http_header.*"
    )
    
    warnings.filterwarnings(
        "ignore",
        message="open_text is deprecated.*",
        category=DeprecationWarning,
        module="importlib.resources.*"
    )
    
    # Additional common external dependency warnings
    warnings.filterwarnings(
        "ignore",
        message=".*deprecated.*",
        category=DeprecationWarning,
        module="fake_http_header.*"
    )


def configure_warnings_for_production() -> None:
    """
    Configure warnings for production environment.
    
    Suppresses non-critical warnings while keeping important ones visible.
    """
    suppress_external_warnings()
    
    # Keep important warnings visible
    warnings.filterwarnings(
        "default",
        category=UserWarning,
        module="crawler.*"
    )
    
    warnings.filterwarnings(
        "default",
        category=UserWarning,
        module="agent.*"
    )


def configure_warnings_for_development() -> None:
    """
    Configure warnings for development environment.
    
    Shows more warnings for debugging but still suppresses external noise.
    """
    suppress_external_warnings()
    
    # Show all warnings from our code
    warnings.filterwarnings(
        "default",
        module="crawler.*"
    )
    
    warnings.filterwarnings(
        "default",
        module="agent.*"
    )
    
    warnings.filterwarnings(
        "default",
        module="utils.*"
    )


def configure_warnings_for_testing() -> None:
    """
    Configure warnings for testing environment.
    
    Suppresses external warnings but shows all warnings from our code.
    """
    suppress_external_warnings()
    
    # Show all warnings from our code during testing
    warnings.filterwarnings(
        "default",
        module="crawler.*"
    )
    
    warnings.filterwarnings(
        "default",
        module="agent.*"
    )
    
    warnings.filterwarnings(
        "default",
        module="utils.*"
    )
    
    warnings.filterwarnings(
        "default",
        module="tests.*"
    )


def get_suppressed_warning_patterns() -> List[Tuple[str, str]]:
    """
    Get a list of warning patterns that are being suppressed.
    
    Returns:
        List of tuples containing (pattern, reason) for documentation
    """
    return [
        (
            "Support for class-based `config` is deprecated",
            "Pydantic v2 deprecation in external dependencies"
        ),
        (
            "read_text is deprecated",
            "importlib.resources deprecation in fake_http_header"
        ),
        (
            "open_text is deprecated", 
            "importlib.resources deprecation in fake_http_header"
        ),
    ]


# Auto-configure warnings based on environment
def auto_configure_warnings() -> None:
    """
    Automatically configure warnings based on the current environment.
    
    Detects if we're in testing, development, or production and applies
    appropriate warning filters.
    """
    import sys
    import os
    
    # Check if we're running tests
    if 'pytest' in sys.modules or 'PYTEST_CURRENT_TEST' in os.environ:
        configure_warnings_for_testing()
    # Check if we're in development (DEBUG mode or development indicators)
    elif (os.environ.get('DEBUG', '').lower() in ('true', '1', 'yes') or
          os.environ.get('ENVIRONMENT', '').lower() == 'development'):
        configure_warnings_for_development()
    else:
        # Default to production configuration
        configure_warnings_for_production()
