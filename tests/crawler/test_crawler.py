"""
Comprehensive tests for crawler functions.

This module tests all crawler functionality including single URL crawling,
multiple URL batch processing, link extraction, and impressum crawling.
"""

import pytest
import asyncio
import os
import tempfile
from unittest.mock import patch, MagicMock
from typing import List, Dict

# Import the functions to test
from crawler import (
    crawl_website,
    crawl_multiple_websites,
    crawl_local_file,
    save_links_to_file,
    _extract_and_categorize_links,
    _extract_page_title,
    download_html,
    download_multiple_html
)
from crawler.company_data import (
    crawl_impressum,
    crawl_multiple_impressums
)


class TestCrawlWebsite:
    """Test the crawl_website function."""

    @pytest.mark.asyncio
    async def test_crawl_website_success(self):
        """Test successful website crawling."""
        url = "https://www.example.com"

        result = await crawl_website(url, screenshot=False, pdf=False, extract_links=False)
        markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

        # Verify basic results
        assert markdown_content is not None
        assert len(markdown_content) > 0
        assert html_path is not None
        assert os.path.exists(html_path)
        assert screenshot_path is None  # Not requested
        assert pdf_path is None  # Not requested
        assert links_data is None  # Not requested
        assert page_title is not None

    @pytest.mark.asyncio
    async def test_crawl_website_with_options(self):
        """Test website crawling with screenshot and PDF options."""
        url = "https://www.example.com"

        result = await crawl_website(url, screenshot=True, pdf=True, extract_links=False)
        markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

        # Verify all results
        assert markdown_content is not None
        assert html_path is not None
        assert screenshot_path is not None
        assert pdf_path is not None
        assert os.path.exists(html_path)
        assert os.path.exists(screenshot_path)
        assert os.path.exists(pdf_path)

    @pytest.mark.asyncio
    async def test_crawl_website_with_links(self):
        """Test website crawling with link extraction."""
        url = "https://www.example.com"

        result = await crawl_website(url, screenshot=False, pdf=False, extract_links=True)
        markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

        # Verify link extraction
        assert links_data is not None
        assert isinstance(links_data, dict)
        assert 'internal' in links_data
        assert 'external' in links_data
        assert isinstance(links_data['internal'], list)
        assert isinstance(links_data['external'], list)

    @pytest.mark.asyncio
    async def test_crawl_website_invalid_url(self):
        """Test crawling with invalid URL."""
        url = "https://invalid-domain-that-does-not-exist.com"

        with pytest.raises(Exception):
            await crawl_website(url)

    @pytest.mark.asyncio
    async def test_crawl_website_with_html_options(self):
        """Test crawl_website with HTML download options."""
        url = "https://www.example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            result = await crawl_website(
                url,
                screenshot=False,
                pdf=False,
                extract_links=False,
                save_html=True,
                html_dir=temp_dir
            )
            markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

            # Verify basic results
            assert markdown_content is not None
            assert len(markdown_content) > 0
            assert html_path is not None
            assert os.path.exists(html_path)
            assert html_path.startswith(temp_dir)
            assert screenshot_path is None
            assert pdf_path is None
            assert links_data is None
            assert page_title is not None

    @pytest.mark.asyncio
    async def test_crawl_website_disable_html_save(self):
        """Test crawl_website with HTML saving disabled."""
        url = "https://www.example.com"

        result = await crawl_website(
            url,
            screenshot=False,
            pdf=False,
            extract_links=False,
            save_html=False  # Disable HTML saving
        )
        markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

        # Verify results
        assert markdown_content is not None
        assert len(markdown_content) > 0
        assert html_path is None  # Should be None when save_html=False
        assert screenshot_path is None
        assert pdf_path is None
        assert links_data is None
        assert page_title is not None


class TestCrawlMultipleWebsites:
    """Test the crawl_multiple_websites function."""

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_list(self):
        """Test crawling multiple websites with a list of URLs."""
        urls = ["https://www.example.com", "https://www.google.com"]

        results = await crawl_multiple_websites(urls)

        # Verify results structure
        assert isinstance(results, list)
        assert len(results) == 2

        for result in results:
            assert isinstance(result, dict)
            assert 'url' in result
            assert 'success' in result
            assert 'page_title' in result
            assert 'markdown_content' in result
            assert 'html_path' in result

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_single_string(self):
        """Test crawling with single URL as string."""
        url = "https://www.example.com"

        results = await crawl_multiple_websites(url)

        # Should return list with one result
        assert isinstance(results, list)
        assert len(results) == 1
        assert results[0]['url'] == url
        assert results[0]['success'] is True

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_mixed_success(self):
        """Test crawling with mix of valid and invalid URLs."""
        urls = [
            "https://www.example.com",
            "https://invalid-domain-that-does-not-exist.com"
        ]

        results = await crawl_multiple_websites(urls)

        # Verify mixed results
        assert len(results) == 2
        assert results[0]['success'] is True
        assert results[1]['success'] is False
        assert results[1]['error'] is not None

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_with_links(self):
        """Test multiple website crawling with link extraction."""
        urls = ["https://www.example.com"]

        results = await crawl_multiple_websites(urls, extract_links=True)

        # Verify link extraction
        assert len(results) == 1
        result = results[0]
        assert result['success'] is True
        assert result['links_data'] is not None
        assert 'internal' in result['links_data']
        assert 'external' in result['links_data']

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_with_html_options(self):
        """Test crawl_multiple_websites with HTML download options."""
        urls = ["https://www.example.com", "https://httpbin.org/html"]

        with tempfile.TemporaryDirectory() as temp_dir:
            results = await crawl_multiple_websites(
                urls,
                screenshot=False,
                pdf=False,
                extract_links=False,
                save_html=True,
                html_dir=temp_dir
            )

            # Verify results structure
            assert isinstance(results, list)
            assert len(results) == len(urls)

            # Check each result
            for i, result in enumerate(results):
                assert result['url'] == urls[i]
                if result['success']:
                    assert result['html_path'] is not None
                    assert os.path.exists(result['html_path'])
                    assert result['html_path'].startswith(temp_dir)
                    assert result['markdown_content'] is not None

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_disable_html_save(self):
        """Test crawl_multiple_websites with HTML saving disabled."""
        urls = ["https://www.example.com"]

        results = await crawl_multiple_websites(
            urls,
            save_html=False  # Disable HTML saving
        )

        # Verify results
        assert isinstance(results, list)
        assert len(results) == 1

        result = results[0]
        if result['success']:
            # Should be None when save_html=False
            assert result['html_path'] is None
            assert result['markdown_content'] is not None


class TestCrawlImpressum:
    """Test the crawl_impressum function."""

    @pytest.mark.asyncio
    async def test_crawl_impressum_success(self):
        """Test successful impressum crawling."""
        url = "https://www.raumweltenheiss.de"

        markdown_content, html_path = await crawl_impressum(url)

        # Verify results
        if markdown_content:  # Some sites may not have impressum
            assert isinstance(markdown_content, str)
            assert len(markdown_content) > 0
            assert html_path is not None
            assert os.path.exists(html_path)

    @pytest.mark.asyncio
    async def test_crawl_impressum_no_impressum(self):
        """Test impressum crawling on site without impressum."""
        url = "https://www.example.com"

        markdown_content, html_path = await crawl_impressum(url)

        # May return None if no impressum found
        # This is expected behavior
        assert markdown_content is None or isinstance(markdown_content, str)


class TestCrawlMultipleImpressums:
    """Test the crawl_multiple_impressums function."""

    @pytest.mark.asyncio
    async def test_crawl_multiple_impressums_list(self):
        """Test crawling multiple impressums with a list of URLs."""
        urls = ["https://www.example.com", "https://www.raumweltenheiss.de"]

        results = await crawl_multiple_impressums(urls)

        # Verify results structure
        assert isinstance(results, list)
        assert len(results) == 2

        for result in results:
            assert isinstance(result, dict)
            assert 'url' in result
            assert 'success' in result
            assert 'markdown_content' in result
            assert 'html_path' in result

    @pytest.mark.asyncio
    async def test_crawl_multiple_impressums_single_string(self):
        """Test crawling impressum with single URL as string."""
        url = "https://www.raumweltenheiss.de"

        results = await crawl_multiple_impressums(url)

        # Should return list with one result
        assert isinstance(results, list)
        assert len(results) == 1
        assert results[0]['url'] == url


class TestCrawlLocalFile:
    """Test the crawl_local_file function."""

    @pytest.mark.asyncio
    async def test_crawl_local_file_success(self):
        """Test successful local file crawling."""
        # Create a temporary HTML file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(
                '<html><head><title>Test</title></head><body><h1>Test Content</h1></body></html>')
            temp_file = f.name

        try:
            result = await crawl_local_file(temp_file)

            # Verify result
            assert result is not None
            assert isinstance(result, str)
            assert len(result) > 0
            assert 'Test Content' in result

        finally:
            # Clean up
            os.unlink(temp_file)

    @pytest.mark.asyncio
    async def test_crawl_local_file_not_found(self):
        """Test local file crawling with non-existent file."""
        result = await crawl_local_file("/path/that/does/not/exist.html")

        # Should return None for non-existent file
        assert result is None


class TestLinkExtraction:
    """Test link extraction functions."""

    def test_extract_and_categorize_links(self):
        """Test link extraction and categorization."""
        base_url = "https://www.example.com"

        # Mock links data from crawl4ai
        mock_links = {
            'internal': [
                {'href': 'https://www.example.com/page1',
                    'text': 'Page 1', 'title': 'First Page'},
                {'href': '/page2', 'text': 'Page 2', 'title': ''}
            ],
            'external': [
                {'href': 'https://www.google.com',
                    'text': 'Google', 'title': 'Search Engine'}
            ]
        }

        result = _extract_and_categorize_links(base_url, mock_links)

        # Verify structure
        assert isinstance(result, dict)
        assert 'internal' in result
        assert 'external' in result
        assert len(result['internal']) == 2
        assert len(result['external']) == 1

        # Verify link objects
        internal_link = result['internal'][0]
        assert 'url' in internal_link
        assert 'title' in internal_link
        assert 'text' in internal_link
        assert 'html_title' in internal_link

    def test_save_links_to_file(self):
        """Test saving links to JSON file."""
        links_data = {
            'internal': [
                {'url': 'https://example.com/page1', 'title': 'Page 1',
                    'text': 'Page 1', 'html_title': ''}
            ],
            'external': [
                {'url': 'https://google.com', 'title': 'Google',
                    'text': 'Google', 'html_title': 'Search'}
            ]
        }

        base_url = "https://www.example.com"
        page_title = "Example Domain"

        file_path = save_links_to_file(links_data, base_url, page_title)

        # Verify file was created
        assert file_path is not None
        assert os.path.exists(file_path)

        # Verify file content
        import json
        with open(file_path, 'r') as f:
            data = json.load(f)

        assert data['base_url'] == base_url
        assert data['page_title'] == page_title
        assert 'links' in data
        assert 'summary' in data

        # Clean up
        os.unlink(file_path)


class TestPageTitleExtraction:
    """Test page title extraction."""

    def test_extract_page_title_from_title_attribute(self):
        """Test extracting title from result.title attribute."""
        mock_result = MagicMock()
        mock_result.title = "Test Page Title"

        title = _extract_page_title(mock_result)
        assert title == "Test Page Title"

    def test_extract_page_title_from_html(self):
        """Test extracting title from HTML content."""
        mock_result = MagicMock()
        mock_result.title = None
        mock_result.metadata = None
        mock_result.html = "<html><head><title>HTML Title</title></head></html>"

        title = _extract_page_title(mock_result)
        assert title == "HTML Title"

    def test_extract_page_title_fallback(self):
        """Test fallback when no title is found."""
        mock_result = MagicMock()
        mock_result.title = None
        mock_result.metadata = None
        mock_result.html = "<html><head></head></html>"
        mock_result.markdown = "# Some Header\nContent"

        title = _extract_page_title(mock_result)
        assert title == "Some Header"


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
