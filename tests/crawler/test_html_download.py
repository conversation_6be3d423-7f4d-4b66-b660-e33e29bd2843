"""
Tests for HTML download functionality.

This module tests the new HTML download functions and extended crawl functions
with HTML download options.
"""

import pytest
import asyncio
import os
import tempfile
import shutil
from unittest.mock import patch, MagicMock
from typing import List, Dict

# Import the functions to test
from crawler import (
    download_html,
    download_multiple_html,
    crawl_website,
    crawl_multiple_websites
)
from utils.file_utils import save_html_to_dir


class TestDownloadHtml:
    """Test the download_html function."""

    @pytest.mark.asyncio
    async def test_download_html_success(self):
        """Test successful HTML download."""
        url = "https://www.example.com"

        # Create temporary directory for test
        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title, screenshot_path, pdf_path = await download_html(
                url=url,
                html_dir=temp_dir
            )

            # Verify results
            assert html_path is not None
            assert os.path.exists(html_path)
            assert html_path.startswith(temp_dir)
            assert html_path.endswith('.html')
            assert page_title is not None
            # Screenshot and PDF should be None when not requested
            assert screenshot_path is None
            assert pdf_path is None

            # Verify HTML content was saved
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
                assert len(html_content) > 0
                assert '<html' in html_content.lower()

    @pytest.mark.asyncio
    async def test_download_html_with_screenshot(self):
        """Test HTML download with screenshot option."""
        url = "https://www.example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title, screenshot_path, pdf_path = await download_html(
                url=url,
                html_dir=temp_dir,
                screenshot=True,
                pdf=False
            )

            # Verify results
            assert html_path is not None
            assert os.path.exists(html_path)
            assert page_title is not None
            # Screenshot path may be None if capture fails, but should be included
            # PDF should be None when not requested
            assert pdf_path is None

    @pytest.mark.asyncio
    async def test_download_html_with_pdf(self):
        """Test HTML download with PDF option."""
        url = "https://www.example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title, screenshot_path, pdf_path = await download_html(
                url=url,
                html_dir=temp_dir,
                screenshot=False,
                pdf=True
            )

            # Verify results
            assert html_path is not None
            assert os.path.exists(html_path)
            assert page_title is not None
            # PDF path may be None if generation fails, but should be included
            # Screenshot should be None when not requested
            assert screenshot_path is None

    @pytest.mark.asyncio
    async def test_download_html_with_both_media(self):
        """Test HTML download with both screenshot and PDF options."""
        url = "https://www.example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title, screenshot_path, pdf_path = await download_html(
                url=url,
                html_dir=temp_dir,
                screenshot=True,
                pdf=True
            )

            # Verify results
            assert html_path is not None
            assert os.path.exists(html_path)
            assert page_title is not None
            # Both media paths may be None if capture/generation fails, but should be included

    @pytest.mark.asyncio
    async def test_download_html_custom_timeout(self):
        """Test HTML download with custom timeout."""
        url = "https://www.example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title, screenshot_path, pdf_path = await download_html(
                url=url,
                html_dir=temp_dir,
                page_timeout=30000,
                wait_until="domcontentloaded"
            )

            # Verify results
            assert html_path is not None
            assert os.path.exists(html_path)

    @pytest.mark.asyncio
    async def test_download_html_invalid_url(self):
        """Test HTML download with invalid URL."""
        url = "https://invalid-domain-that-does-not-exist-12345.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title, screenshot_path, pdf_path = await download_html(
                url=url,
                html_dir=temp_dir
            )

            # Should return None for invalid URLs
            assert html_path is None
            assert page_title is None
            assert screenshot_path is None
            assert pdf_path is None

    @pytest.mark.asyncio
    async def test_download_html_default_directory(self):
        """Test HTML download with default directory."""
        url = "https://httpbin.org/html"

        try:
            html_path, page_title, screenshot_path, pdf_path = await download_html(url=url)

            # Verify results
            assert html_path is not None
            assert os.path.exists(html_path)
            assert html_path.startswith("scraped_html")
            assert "httpbin.org" in html_path

        finally:
            # Clean up default directory
            if os.path.exists("scraped_html"):
                shutil.rmtree("scraped_html")


class TestDownloadMultipleHtml:
    """Test the download_multiple_html function."""

    @pytest.mark.asyncio
    async def test_download_multiple_html_success(self):
        """Test successful multiple HTML downloads."""
        urls = [
            "https://www.example.com",
            "https://httpbin.org/html"
        ]

        with tempfile.TemporaryDirectory() as temp_dir:
            results = await download_multiple_html(
                urls=urls,
                html_dir=temp_dir
            )

            # Verify results structure
            assert isinstance(results, list)
            assert len(results) == len(urls)

            # Check each result
            for i, result in enumerate(results):
                assert result['url'] == urls[i]
                assert 'success' in result
                assert 'page_title' in result
                assert 'html_path' in result
                assert 'error' in result

                if result['success']:
                    assert result['html_path'] is not None
                    assert os.path.exists(result['html_path'])

    @pytest.mark.asyncio
    async def test_download_multiple_html_single_string(self):
        """Test multiple HTML download with single URL as string."""
        url = "https://www.example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            results = await download_multiple_html(
                urls=url,  # Single string instead of list
                html_dir=temp_dir
            )

            # Should return list with one result
            assert isinstance(results, list)
            assert len(results) == 1
            assert results[0]['url'] == url

    @pytest.mark.asyncio
    async def test_download_multiple_html_mixed_results(self):
        """Test multiple HTML download with mix of valid and invalid URLs."""
        urls = [
            "https://www.example.com",
            "https://invalid-domain-that-does-not-exist-12345.com",
            "https://httpbin.org/html"
        ]

        with tempfile.TemporaryDirectory() as temp_dir:
            results = await download_multiple_html(
                urls=urls,
                html_dir=temp_dir
            )

            # Verify results structure
            assert isinstance(results, list)
            assert len(results) == len(urls)

            # Check that we have both successful and failed results
            successful_results = [r for r in results if r['success']]
            failed_results = [r for r in results if not r['success']]

            # At least example.com should work
            assert len(successful_results) >= 1
            assert len(failed_results) >= 1     # Invalid domain should fail

    @pytest.mark.asyncio
    async def test_download_multiple_html_empty_list(self):
        """Test multiple HTML download with empty URL list."""
        urls = []

        with tempfile.TemporaryDirectory() as temp_dir:
            results = await download_multiple_html(
                urls=urls,
                html_dir=temp_dir
            )

            # Should return empty list
            assert isinstance(results, list)
            assert len(results) == 0


class TestExtendedCrawlFunctions:
    """Test the extended crawl functions with HTML download options."""

    @pytest.mark.asyncio
    async def test_crawl_website_with_html_options(self):
        """Test crawl_website with HTML download options."""
        url = "https://www.example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            result = await crawl_website(
                url=url,
                save_html=True,
                html_dir=temp_dir
            )

            markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

            # Verify results
            assert markdown_content is not None
            assert len(markdown_content) > 0
            assert html_path is not None
            assert os.path.exists(html_path)
            assert html_path.startswith(temp_dir)
            assert page_title is not None

    @pytest.mark.asyncio
    async def test_crawl_website_disable_html_save(self):
        """Test crawl_website with HTML saving disabled."""
        url = "https://www.example.com"

        result = await crawl_website(
            url=url,
            save_html=False  # Disable HTML saving
        )

        markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

        # Verify results
        assert markdown_content is not None
        assert len(markdown_content) > 0
        assert html_path is None  # Should be None when save_html=False
        assert page_title is not None

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_with_html_options(self):
        """Test crawl_multiple_websites with HTML download options."""
        urls = [
            "https://www.example.com",
            "https://httpbin.org/html"
        ]

        with tempfile.TemporaryDirectory() as temp_dir:
            results = await crawl_multiple_websites(
                urls=urls,
                save_html=True,
                html_dir=temp_dir
            )

            # Verify results structure
            assert isinstance(results, list)
            assert len(results) == len(urls)

            # Check each result
            for i, result in enumerate(results):
                assert result['url'] == urls[i]
                if result['success']:
                    assert result['html_path'] is not None
                    assert os.path.exists(result['html_path'])
                    assert result['html_path'].startswith(temp_dir)
                    assert result['markdown_content'] is not None

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_disable_html_save(self):
        """Test crawl_multiple_websites with HTML saving disabled."""
        urls = ["https://www.example.com"]

        results = await crawl_multiple_websites(
            urls=urls,
            save_html=False  # Disable HTML saving
        )

        # Verify results
        assert isinstance(results, list)
        assert len(results) == 1

        result = results[0]
        if result['success']:
            # Should be None when save_html=False
            assert result['html_path'] is None
            assert result['markdown_content'] is not None

    @pytest.mark.asyncio
    async def test_crawl_website_with_all_options(self):
        """Test crawl_website with all HTML download options."""
        url = "https://www.example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            result = await crawl_website(
                url=url,
                screenshot=False,
                pdf=False,
                extract_links=False,
                save_html=True,
                html_dir=temp_dir
            )

            markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

            # Verify all results
            assert markdown_content is not None
            assert len(markdown_content) > 0
            assert html_path is not None
            assert os.path.exists(html_path)
            assert html_path.startswith(temp_dir)
            assert screenshot_path is None
            assert pdf_path is None
            assert links_data is None
            assert page_title is not None

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_with_all_options(self):
        """Test crawl_multiple_websites with all HTML download options."""
        urls = ["https://www.example.com", "https://httpbin.org/html"]

        with tempfile.TemporaryDirectory() as temp_dir:
            results = await crawl_multiple_websites(
                urls=urls,
                screenshot=False,
                pdf=False,
                extract_links=False,
                save_html=True,
                html_dir=temp_dir
            )

            # Verify results structure
            assert isinstance(results, list)
            assert len(results) == len(urls)

            # Check each result
            for i, result in enumerate(results):
                assert result['url'] == urls[i]
                if result['success']:
                    assert result['html_path'] is not None
                    assert os.path.exists(result['html_path'])
                    assert result['html_path'].startswith(temp_dir)
                    assert result['markdown_content'] is not None
                    assert result['page_title'] is not None


class TestHtmlDirectoryStructure:
    """Test HTML directory structure and file naming."""

    @pytest.mark.asyncio
    async def test_html_directory_structure(self):
        """Test that HTML files are saved with correct directory structure."""
        url = "https://www.example.com/test/page"

        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, _, _, _ = await download_html(
                url=url,
                html_dir=temp_dir
            )

            # Verify directory structure
            assert html_path is not None
            assert os.path.exists(html_path)

            # Should be saved in domain subdirectory
            expected_domain_dir = os.path.join(temp_dir, "example.com")
            assert os.path.exists(expected_domain_dir)
            assert html_path.startswith(expected_domain_dir)

            # Filename should contain page name and timestamp
            filename = os.path.basename(html_path)
            assert filename.startswith("page_")
            assert filename.endswith(".html")

    @pytest.mark.asyncio
    async def test_custom_html_directory(self):
        """Test using custom HTML directory."""
        url = "https://www.example.com"
        custom_dir = "my_custom_html_dir"

        try:
            html_path, _, _, _ = await download_html(
                url=url,
                html_dir=custom_dir
            )

            # Verify custom directory was used
            assert html_path is not None
            assert os.path.exists(html_path)
            assert html_path.startswith(custom_dir)

        finally:
            # Clean up custom directory
            if os.path.exists(custom_dir):
                shutil.rmtree(custom_dir)


class TestHtmlDownloadIntegration:
    """Test HTML download integration with other features."""

    @pytest.mark.asyncio
    async def test_html_download_with_statistics_tracking(self):
        """Test that HTML downloads are tracked in statistics."""
        url = "https://www.example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title, _, _ = await download_html(
                url=url,
                html_dir=temp_dir
            )

            # Verify download was successful
            assert html_path is not None
            assert os.path.exists(html_path)

            # Note: Statistics tracking is tested separately in test_statistics.py
            # This test just ensures the download works with statistics enabled

    @pytest.mark.asyncio
    async def test_html_download_file_naming(self):
        """Test HTML file naming conventions."""
        urls = [
            "https://www.example.com",
            "https://www.example.com/about",
            "https://www.example.com/contact.html",
            "https://httpbin.org/html"
        ]

        with tempfile.TemporaryDirectory() as temp_dir:
            for url in urls:
                html_path, _, _, _ = await download_html(
                    url=url,
                    html_dir=temp_dir
                )

                if html_path:  # Skip if download failed
                    # Verify file naming
                    filename = os.path.basename(html_path)
                    assert filename.endswith('.html')
                    assert '_' in filename  # Should have timestamp

                    # Verify directory structure
                    domain_dir = os.path.dirname(html_path)
                    domain_name = os.path.basename(domain_dir)
                    assert domain_name in url

    @pytest.mark.asyncio
    async def test_html_download_concurrent_requests(self):
        """Test concurrent HTML downloads."""
        urls = [
            "https://www.example.com",
            "https://httpbin.org/html",
            "https://www.example.com"  # Duplicate to test handling
        ]

        with tempfile.TemporaryDirectory() as temp_dir:
            # Run downloads concurrently
            tasks = [
                download_html(url, html_dir=temp_dir)
                for url in urls
            ]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Verify results
            successful_downloads = 0
            for result in results:
                if not isinstance(result, Exception):
                    html_path, page_title = result
                    if html_path and os.path.exists(html_path):
                        successful_downloads += 1

            # At least some downloads should succeed
            assert successful_downloads >= 1

    @pytest.mark.asyncio
    async def test_save_html_to_dir_function(self):
        """Test the save_html_to_dir utility function directly."""
        html_content = "<html><head><title>Test</title></head><body>Test content</body></html>"
        url = "https://www.example.com/test"

        with tempfile.TemporaryDirectory() as temp_dir:
            saved_path = save_html_to_dir(html_content, url, temp_dir)

            # Verify file was saved
            assert saved_path is not None
            assert os.path.exists(saved_path)
            assert saved_path.startswith(temp_dir)

            # Verify content
            with open(saved_path, 'r', encoding='utf-8') as f:
                saved_content = f.read()
                assert saved_content == html_content

            # Verify directory structure
            assert "example.com" in saved_path
            assert saved_path.endswith('.html')


class TestHtmlDownloadErrorHandling:
    """Test error handling in HTML download functions."""

    @pytest.mark.asyncio
    async def test_download_html_network_error(self):
        """Test HTML download with network errors."""
        # Use a URL that should cause a network error
        url = "https://localhost:99999/nonexistent"

        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title, _, _ = await download_html(
                url=url,
                html_dir=temp_dir,
                page_timeout=5000  # Short timeout
            )

            # Should handle error gracefully
            assert html_path is None
            assert page_title is None

    @pytest.mark.asyncio
    async def test_download_multiple_html_partial_failure(self):
        """Test multiple HTML download with some failures."""
        urls = [
            "https://www.example.com",  # Should work
            "https://localhost:99999/nonexistent",  # Should fail
            "https://httpbin.org/html"  # Should work
        ]

        with tempfile.TemporaryDirectory() as temp_dir:
            results = await download_multiple_html(
                urls=urls,
                html_dir=temp_dir,
                page_timeout=10000
            )

            # Verify results structure
            assert isinstance(results, list)
            assert len(results) == len(urls)

            # Should have both successful and failed results
            successful = [r for r in results if r['success']]
            failed = [r for r in results if not r['success']]

            assert len(successful) >= 1  # At least one should succeed
            assert len(failed) >= 1      # At least one should fail

            # Verify error information is included
            for failed_result in failed:
                assert 'error' in failed_result
                assert failed_result['error'] is not None
