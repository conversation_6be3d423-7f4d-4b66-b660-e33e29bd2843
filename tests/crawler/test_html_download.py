"""
Tests for HTML download functionality.

This module tests the new HTML download functions and extended crawl functions
with HTML download options.
"""

import pytest
import asyncio
import os
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# Import the functions to test
from crawler import (
    download_html,
    download_multiple_html,
    crawl_website,
    crawl_multiple_websites
)


class TestDownloadHtml:
    """Test the download_html function."""

    @pytest.mark.asyncio
    async def test_download_html_success(self):
        """Test successful HTML download."""
        url = "https://www.example.com"
        
        # Create temporary directory for test
        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title = await download_html(
                url=url,
                html_dir=temp_dir
            )
            
            # Verify results
            assert html_path is not None
            assert os.path.exists(html_path)
            assert html_path.startswith(temp_dir)
            assert html_path.endswith('.html')
            assert page_title is not None
            
            # Verify HTML content was saved
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
                assert len(html_content) > 0
                assert '<html' in html_content.lower()

    @pytest.mark.asyncio
    async def test_download_html_custom_timeout(self):
        """Test HTML download with custom timeout."""
        url = "https://www.example.com"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, page_title = await download_html(
                url=url,
                html_dir=temp_dir,
                page_timeout=30000,
                wait_until="domcontentloaded"
            )
            
            # Verify results
            assert html_path is not None
            assert os.path.exists(html_path)


class TestDownloadMultipleHtml:
    """Test the download_multiple_html function."""

    @pytest.mark.asyncio
    async def test_download_multiple_html_success(self):
        """Test successful multiple HTML downloads."""
        urls = [
            "https://www.example.com",
            "https://httpbin.org/html"
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            results = await download_multiple_html(
                urls=urls,
                html_dir=temp_dir
            )
            
            # Verify results structure
            assert isinstance(results, list)
            assert len(results) == len(urls)
            
            # Check each result
            for i, result in enumerate(results):
                assert result['url'] == urls[i]
                assert 'success' in result
                assert 'page_title' in result
                assert 'html_path' in result
                assert 'error' in result
                
                if result['success']:
                    assert result['html_path'] is not None
                    assert os.path.exists(result['html_path'])

    @pytest.mark.asyncio
    async def test_download_multiple_html_single_string(self):
        """Test multiple HTML download with single URL as string."""
        url = "https://www.example.com"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            results = await download_multiple_html(
                urls=url,  # Single string instead of list
                html_dir=temp_dir
            )
            
            # Should return list with one result
            assert isinstance(results, list)
            assert len(results) == 1
            assert results[0]['url'] == url


class TestExtendedCrawlFunctions:
    """Test the extended crawl functions with HTML download options."""

    @pytest.mark.asyncio
    async def test_crawl_website_with_html_options(self):
        """Test crawl_website with HTML download options."""
        url = "https://www.example.com"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            result = await crawl_website(
                url=url,
                save_html=True,
                html_dir=temp_dir
            )
            
            markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result
            
            # Verify results
            assert markdown_content is not None
            assert len(markdown_content) > 0
            assert html_path is not None
            assert os.path.exists(html_path)
            assert html_path.startswith(temp_dir)
            assert page_title is not None

    @pytest.mark.asyncio
    async def test_crawl_website_disable_html_save(self):
        """Test crawl_website with HTML saving disabled."""
        url = "https://www.example.com"
        
        result = await crawl_website(
            url=url,
            save_html=False  # Disable HTML saving
        )
        
        markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result
        
        # Verify results
        assert markdown_content is not None
        assert len(markdown_content) > 0
        assert html_path is None  # Should be None when save_html=False
        assert page_title is not None

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_with_html_options(self):
        """Test crawl_multiple_websites with HTML download options."""
        urls = [
            "https://www.example.com",
            "https://httpbin.org/html"
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            results = await crawl_multiple_websites(
                urls=urls,
                save_html=True,
                html_dir=temp_dir
            )
            
            # Verify results structure
            assert isinstance(results, list)
            assert len(results) == len(urls)
            
            # Check each result
            for i, result in enumerate(results):
                assert result['url'] == urls[i]
                if result['success']:
                    assert result['html_path'] is not None
                    assert os.path.exists(result['html_path'])
                    assert result['html_path'].startswith(temp_dir)
                    assert result['markdown_content'] is not None

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_disable_html_save(self):
        """Test crawl_multiple_websites with HTML saving disabled."""
        urls = ["https://www.example.com"]
        
        results = await crawl_multiple_websites(
            urls=urls,
            save_html=False  # Disable HTML saving
        )
        
        # Verify results
        assert isinstance(results, list)
        assert len(results) == 1
        
        result = results[0]
        if result['success']:
            assert result['html_path'] is None  # Should be None when save_html=False
            assert result['markdown_content'] is not None


class TestHtmlDirectoryStructure:
    """Test HTML directory structure and file naming."""

    @pytest.mark.asyncio
    async def test_html_directory_structure(self):
        """Test that HTML files are saved with correct directory structure."""
        url = "https://www.example.com/test/page"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            html_path, _ = await download_html(
                url=url,
                html_dir=temp_dir
            )
            
            # Verify directory structure
            assert html_path is not None
            assert os.path.exists(html_path)
            
            # Should be saved in domain subdirectory
            expected_domain_dir = os.path.join(temp_dir, "example.com")
            assert os.path.exists(expected_domain_dir)
            assert html_path.startswith(expected_domain_dir)
            
            # Filename should contain page name and timestamp
            filename = os.path.basename(html_path)
            assert filename.startswith("page_")
            assert filename.endswith(".html")

    @pytest.mark.asyncio
    async def test_custom_html_directory(self):
        """Test using custom HTML directory."""
        url = "https://www.example.com"
        custom_dir = "my_custom_html_dir"
        
        try:
            html_path, _ = await download_html(
                url=url,
                html_dir=custom_dir
            )
            
            # Verify custom directory was used
            assert html_path is not None
            assert os.path.exists(html_path)
            assert html_path.startswith(custom_dir)
            
        finally:
            # Clean up custom directory
            if os.path.exists(custom_dir):
                shutil.rmtree(custom_dir)
