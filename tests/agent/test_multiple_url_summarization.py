"""
Tests for multiple URL summarization functionality.

This module tests the LangGraph integration for multiple URL summarization,
including intent parsing, workflow routing, summary generation, and result formatting.
"""

import pytest
import asyncio
import os
import tempfile
from unittest.mock import patch, MagicMock
from typing import List, Dict

# Import LangGraph components
from agent.langgraph_state import create_initial_state, WorkflowCommands
from agent.langgraph_nodes import (
    intent_parser_node, 
    crawl_multiple_websites_node,
    generate_multiple_summaries_node,
    result_formatter_node
)
from agent.langgraph_tools import (
    crawl_multiple_websites_tool,
    generate_summary_tool,
    save_summary_tool
)


class TestMultipleUrlSummarizationIntentParsing:
    """Test intent parsing for multiple URL summarization."""

    @pytest.mark.asyncio
    async def test_intent_parsing_multiple_url_summarization(self):
        """Test intent parsing for multiple URL summarization commands."""
        test_prompts = [
            "summarize https://example.com, https://httpbin.org/html",
            "summarize example.com, google.com",
            "generate summaries for https://site1.com, https://site2.com",
            "summarise example.com,httpbin.org/html"
        ]

        for prompt in test_prompts:
            state = create_initial_state(prompt)
            result = await intent_parser_node(state)

            # Verify intent parsing
            assert result['parsed_intent']['command'] == 'crawl_multiple_websites'
            assert result['next_action'] == WorkflowCommands.CRAWL_MULTIPLE_WEBSITES
            assert result['urls'] is not None
            assert len(result['urls']) >= 2

    @pytest.mark.asyncio
    async def test_intent_parsing_single_url_summarization(self):
        """Test that single URL summarization still works."""
        test_prompts = [
            "summarize https://example.com",
            "generate summary for example.com"
        ]

        for prompt in test_prompts:
            state = create_initial_state(prompt)
            result = await intent_parser_node(state)

            # Should be detected as single URL operation
            assert result['parsed_intent']['command'] == 'summarize'
            assert result['next_action'] == WorkflowCommands.CRAWL_URL
            assert result['url'] is not None


class TestMultipleWebsitesCrawlNode:
    """Test the crawl_multiple_websites_node for summarization."""

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_node_with_summarization(self):
        """Test crawl_multiple_websites_node detects summarization request."""
        urls = ["https://www.example.com", "https://httpbin.org/html"]
        
        state = {
            'user_prompt': 'summarize https://www.example.com, https://httpbin.org/html',
            'urls': urls,
            'screenshot': False,
            'pdf': False,
            'parsed_intent': {'extract_links': False}
        }

        result = await crawl_multiple_websites_node(state)

        # Should route to summary generation
        assert result['next_action'] == WorkflowCommands.GENERATE_MULTIPLE_SUMMARIES
        assert result['multiple_crawl_results'] is not None
        assert len(result['multiple_crawl_results']) == len(urls)
        assert result['successful_crawls'] >= 1

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_node_without_summarization(self):
        """Test crawl_multiple_websites_node without summarization request."""
        urls = ["https://www.example.com", "https://httpbin.org/html"]
        
        state = {
            'user_prompt': 'crawl https://www.example.com, https://httpbin.org/html',
            'urls': urls,
            'screenshot': False,
            'pdf': False,
            'parsed_intent': {'extract_links': False}
        }

        result = await crawl_multiple_websites_node(state)

        # Should route to result formatting (no summarization)
        assert result['next_action'] == WorkflowCommands.FORMAT_OUTPUT
        assert result['multiple_crawl_results'] is not None
        assert len(result['multiple_crawl_results']) == len(urls)


class TestGenerateMultipleSummariesNode:
    """Test the generate_multiple_summaries_node."""

    @pytest.mark.asyncio
    async def test_generate_multiple_summaries_node_success(self):
        """Test successful multiple summary generation."""
        # Mock crawled results
        mock_crawl_results = [
            {
                'url': 'https://example.com',
                'success': True,
                'markdown_content': '# Example Domain\nThis is a test domain.',
                'page_title': 'Example Domain'
            },
            {
                'url': 'https://httpbin.org/html',
                'success': True,
                'markdown_content': '# HTTP Test Page\nThis is a test page.',
                'page_title': 'HTTP Test'
            }
        ]

        state = {
            'multiple_crawl_results': mock_crawl_results,
            'user_prompt': 'summarize example.com, httpbin.org/html'
        }

        # Mock the summary generation tools
        with patch('agent.langgraph_tools.generate_summary_tool') as mock_generate, \
             patch('agent.langgraph_tools.save_summary_tool') as mock_save:
            
            # Mock successful summary generation
            mock_generate.ainvoke.return_value = {
                'summary': 'This is a test summary.',
                'success': True
            }
            mock_save.invoke.return_value = {
                'summary_path': '/path/to/summary.md',
                'success': True
            }

            result = await generate_multiple_summaries_node(state)

            # Verify results
            assert result['next_action'] == WorkflowCommands.FORMAT_OUTPUT
            assert result['successful_summaries'] == 2
            assert result['failed_summaries'] == 0
            assert len(result['multiple_crawl_results']) == 2

            # Verify summaries were added to results
            for crawl_result in result['multiple_crawl_results']:
                assert 'summary' in crawl_result
                assert crawl_result['summary'] is not None
                assert 'summary_path' in crawl_result

    @pytest.mark.asyncio
    async def test_generate_multiple_summaries_node_no_results(self):
        """Test generate_multiple_summaries_node with no crawled results."""
        state = {
            'multiple_crawl_results': [],
            'user_prompt': 'summarize example.com, httpbin.org/html'
        }

        result = await generate_multiple_summaries_node(state)

        # Should return error
        assert result['next_action'] == WorkflowCommands.ERROR
        assert 'error' in result
        assert 'No crawled results found' in result['error']

    @pytest.mark.asyncio
    async def test_generate_multiple_summaries_node_partial_failure(self):
        """Test generate_multiple_summaries_node with some failed summaries."""
        # Mock crawled results with one failed crawl
        mock_crawl_results = [
            {
                'url': 'https://example.com',
                'success': True,
                'markdown_content': '# Example Domain\nThis is a test domain.',
                'page_title': 'Example Domain'
            },
            {
                'url': 'https://invalid.com',
                'success': False,
                'markdown_content': None,
                'page_title': None,
                'error': 'Failed to crawl'
            }
        ]

        state = {
            'multiple_crawl_results': mock_crawl_results,
            'user_prompt': 'summarize example.com, invalid.com'
        }

        # Mock the summary generation tools
        with patch('agent.langgraph_tools.generate_summary_tool') as mock_generate, \
             patch('agent.langgraph_tools.save_summary_tool') as mock_save:
            
            # Mock successful summary generation for valid content
            mock_generate.ainvoke.return_value = {
                'summary': 'This is a test summary.',
                'success': True
            }
            mock_save.invoke.return_value = {
                'summary_path': '/path/to/summary.md',
                'success': True
            }

            result = await generate_multiple_summaries_node(state)

            # Verify results
            assert result['next_action'] == WorkflowCommands.FORMAT_OUTPUT
            assert result['successful_summaries'] == 1
            assert result['failed_summaries'] == 1


class TestMultipleUrlSummarizationWorkflow:
    """Test the complete multiple URL summarization workflow."""

    @pytest.mark.asyncio
    async def test_complete_summarization_workflow(self):
        """Test the complete workflow from intent parsing to result formatting."""
        prompt = "summarize https://example.com, https://httpbin.org/html"
        
        # Step 1: Intent parsing
        state = create_initial_state(prompt)
        state = await intent_parser_node(state)
        
        assert state['parsed_intent']['command'] == 'crawl_multiple_websites'
        assert state['next_action'] == WorkflowCommands.CRAWL_MULTIPLE_WEBSITES
        assert len(state['urls']) == 2

        # Step 2: Multiple websites crawling (with summarization detection)
        state = await crawl_multiple_websites_node(state)
        
        assert state['next_action'] == WorkflowCommands.GENERATE_MULTIPLE_SUMMARIES
        assert state['multiple_crawl_results'] is not None
        assert state['successful_crawls'] >= 1

        # Step 3: Summary generation (mock the LLM calls)
        with patch('agent.langgraph_tools.generate_summary_tool') as mock_generate, \
             patch('agent.langgraph_tools.save_summary_tool') as mock_save:
            
            mock_generate.ainvoke.return_value = {
                'summary': 'This is a test summary.',
                'success': True
            }
            mock_save.invoke.return_value = {
                'summary_path': '/path/to/summary.md',
                'success': True
            }

            state = await generate_multiple_summaries_node(state)
            
            assert state['next_action'] == WorkflowCommands.FORMAT_OUTPUT
            assert state['successful_summaries'] >= 1

        # Step 4: Result formatting
        state = await result_formatter_node(state)
        
        assert state['result'] is not None
        assert 'multiple_crawl_results' in state['result']
        assert 'successful_summaries' in state['result']


class TestMultipleUrlSummarizationTools:
    """Test the tools used in multiple URL summarization."""

    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_tool_for_summarization(self):
        """Test crawl_multiple_websites_tool returns data suitable for summarization."""
        urls = ["https://www.example.com", "https://httpbin.org/html"]

        result = await crawl_multiple_websites_tool.ainvoke({
            "urls": urls,
            "screenshot": False,
            "pdf": False,
            "extract_links": False
        })

        # Verify tool result structure
        assert result['success'] is True
        assert result['total_urls'] == len(urls)
        assert result['successful_crawls'] >= 1
        assert 'results' in result

        # Verify each result has fields needed for summarization
        for crawl_result in result['results']:
            if crawl_result['success']:
                assert 'markdown_content' in crawl_result
                assert 'page_title' in crawl_result
                assert 'url' in crawl_result
                assert crawl_result['markdown_content'] is not None

    @pytest.mark.asyncio
    async def test_generate_summary_tool_integration(self):
        """Test generate_summary_tool with realistic content."""
        test_content = """
        # Example Domain
        This domain is for use in illustrative examples in documents. 
        You may use this domain in literature without prior coordination or asking for permission.
        More information can be found at https://www.iana.org/domains/example
        """

        result = await generate_summary_tool.ainvoke({
            "content": test_content
        })

        # Verify summary generation
        if result.get('success'):
            assert 'summary' in result
            assert result['summary'] is not None
            assert len(result['summary']) > 0
        else:
            # If it fails (e.g., no LLM available), that's also acceptable for testing
            assert 'error' in result

    @pytest.mark.asyncio
    async def test_save_summary_tool_integration(self):
        """Test save_summary_tool functionality."""
        test_summary = "This is a test summary of the website content."
        test_url = "https://example.com"

        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock the save_summary_to_dir function to use temp directory
            with patch('utils.file_utils.save_summary_to_dir') as mock_save:
                mock_save.return_value = os.path.join(temp_dir, "summary.md")
                
                result = save_summary_tool.invoke({
                    "summary": test_summary,
                    "url": test_url
                })

                # Verify save result
                assert result['success'] is True
                assert 'summary_path' in result
                assert result['summary_path'] is not None
