"""
Pytest configuration and fixtures for Crawl4AI Agent tests.

This module provides shared test configuration, fixtures, and warning filters
for all test modules in the project.
"""

import warnings
import pytest
import asyncio
from typing import Generator


def pytest_configure(config):
    """Configure pytest with warning filters and other settings."""

    # Suppress external dependency warnings with more specific patterns
    warnings.filterwarnings(
        "ignore",
        message=".*class-based.*config.*deprecated.*",
        category=DeprecationWarning
    )

    warnings.filterwarnings(
        "ignore",
        message=".*read_text.*deprecated.*",
        category=DeprecationWarning
    )

    warnings.filterwarnings(
        "ignore",
        message=".*open_text.*deprecated.*",
        category=DeprecationWarning
    )

    # Suppress all deprecation warnings from external packages
    warnings.filterwarnings(
        "ignore",
        category=DeprecationWarning,
        module="pydantic.*"
    )

    warnings.filterwarnings(
        "ignore",
        category=DeprecationWarning,
        module="fake_http_header.*"
    )

    warnings.filterwarnings(
        "ignore",
        category=DeprecationWarning,
        module="importlib.*"
    )

    # Keep warnings from our code visible
    warnings.filterwarnings(
        "default",
        module="crawler.*"
    )

    warnings.filterwarnings(
        "default",
        module="agent.*"
    )

    warnings.filterwarnings(
        "default",
        module="utils.*"
    )


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """
    Create an event loop for the entire test session.

    This fixture ensures that async tests have access to a consistent
    event loop throughout the test session.
    """
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()


@pytest.fixture
def suppress_warnings():
    """
    Fixture to temporarily suppress all warnings in specific tests.

    Usage:
        def test_something(suppress_warnings):
            # This test will run without any warnings
            pass
    """
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        yield


@pytest.fixture
def capture_warnings():
    """
    Fixture to capture warnings for testing warning behavior.

    Usage:
        def test_warning_behavior(capture_warnings):
            with capture_warnings:
                # Code that should generate warnings
                pass
            assert len(capture_warnings.list) > 0
    """
    with warnings.catch_warnings(record=True) as warning_list:
        warnings.simplefilter("always")
        yield warning_list


# Pytest markers for test categorization
pytest_plugins = []


def pytest_collection_modifyitems(config, items):
    """
    Modify test items during collection to add markers and configure tests.

    This function automatically adds markers to tests based on their names
    and locations, making it easier to run specific test categories.
    """
    for item in items:
        # Add markers based on test file location
        if "test_langgraph" in item.nodeid:
            item.add_marker(pytest.mark.tools)

        if "test_ollama" in item.nodeid:
            item.add_marker(pytest.mark.ollama)

        if "test_statistics" in item.nodeid:
            item.add_marker(pytest.mark.unit)

        # Add slow marker to integration tests
        if "integration" in item.nodeid or "test_workflow" in item.nodeid:
            item.add_marker(pytest.mark.slow)

        # Add network marker to tests that require internet
        if any(keyword in item.nodeid.lower() for keyword in ["crawl", "download", "web", "url"]):
            item.add_marker(pytest.mark.network)
