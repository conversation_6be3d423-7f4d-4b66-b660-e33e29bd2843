def build_parse_command_prompt(user_prompt: str) -> str:
    """
    Builds a prompt to instruct the LLM to parse a user's natural language
    instruction into a structured command and URL/path.

    Args:
        user_prompt (str): The raw instruction from the user.

    Returns:
        A formatted prompt ready for the LLM.
    """
    return f"""You are an expert command parser for a web crawler agent. Your task is to analyze the user's instruction and determine which command to execute and what the target URL or file path is.

The available commands are:
- "summarize": The user wants to summarize the content of a given URL. Keywords: summarize, summary, give me the gist.
- "impressum": The user wants to find the legal notice/impressum page of a URL and extract company data. Keywords: impressum, legal notice, company data, details, who owns.
- "smart_crawl": The user wants to use AI-powered schema learning to extract structured data from a URL. Keywords: smart crawl, smart crawler, schema crawl, intelligent crawl.
- "sitemap": The user wants to extract URLs from a website's sitemap. Keywords: sitemap, extract sitemap, get sitemap, read sitemap, find sitemap, sitemap urls.
- "download_html": The user wants to download and save HTML content from a URL. Keywords: download html, save html, get html, download page, save page html, html download.
- "local": The user wants to process a local file. The user will provide a file path. Keywords: local, file, read, process file.
- "statistics": The user wants to view crawl statistics summary. Keywords: statistics, stats, crawl stats, summary, report, analytics.
- "domain_stats": The user wants to analyze performance for a specific domain. Keywords: domain stats, domain analysis, domain performance, analyze domain.
- "recent_activity": The user wants to see recent crawl activity. Keywords: recent activity, recent crawls, last crawls, activity log.
- "preview_historical_import": The user wants to preview historical data import. Keywords: preview historical import, preview import historical, show historical import preview.
- "import_historical_data": The user wants to import historical crawl data. Keywords: import historical data, import historical crawl data, load historical data.

- "chat": The user is just having a conversation, asking a general question, or not issuing a command that involves a URL or file path.

The user's instruction is: "{user_prompt}"

Analyze the instruction and generate a JSON object with two keys: "command" and "url".
- The "command" value MUST be one of "summarize", "impressum", "smart_crawl", "sitemap", "download_html", "local", "statistics", "domain_stats", "recent_activity", "preview_historical_import", "import_historical_data", or "chat".
- The "url" value MUST be the URL or file path extracted from the instruction. If no URL or path is present or needed, the value should be null.

---
EXAMPLES:
- User instruction: "hey can you summarize https://example.com for me?" -> {{"command": "summarize", "url": "https://example.com"}}
- User instruction: "find the company details for example.com" -> {{"command": "impressum", "url": "example.com"}}
- User instruction: "smart crawl https://shop.com for product data" -> {{"command": "smart_crawl", "url": "https://shop.com"}}
- User instruction: "use schema learning to extract data from example.com" -> {{"command": "smart_crawl", "url": "example.com"}}
- User instruction: "extract sitemap from docs.python.org" -> {{"command": "sitemap", "url": "docs.python.org"}}
- User instruction: "get sitemap urls from https://example.com" -> {{"command": "sitemap", "url": "https://example.com"}}
- User instruction: "read sitemap from example.com" -> {{"command": "sitemap", "url": "example.com"}}
- User instruction: "download html from example.com" -> {{"command": "download_html", "url": "example.com"}}
- User instruction: "save html from https://example.com" -> {{"command": "download_html", "url": "https://example.com"}}
- User instruction: "get html content from example.com" -> {{"command": "download_html", "url": "example.com"}}
- User instruction: "process the local file /path/to/my/document.html" -> {{"command": "local", "url": "/path/to/my/document.html"}}
- User instruction: "show me crawl statistics" -> {{"command": "statistics", "url": null}}
- User instruction: "analyze domain performance for example.com" -> {{"command": "domain_stats", "url": "example.com"}}
- User instruction: "show recent crawl activity" -> {{"command": "recent_activity", "url": null}}
- User instruction: "preview historical import" -> {{"command": "preview_historical_import", "url": null}}
- User instruction: "import historical data" -> {{"command": "import_historical_data", "url": null}}

- User instruction: "what is the capital of France?" -> {{"command": "chat", "url": null}}
---

User instruction: "{user_prompt}"
Your response:"""
