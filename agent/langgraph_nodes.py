"""
LangGraph Nodes for the Crawl4AI Agent

This module contains the workflow nodes that process the agent state
and perform specific tasks in the crawling and data extraction pipeline.
"""

from typing import List
from langchain_ollama import ChatOllama
from langchain_core.messages import HumanMessage, SystemMessage

from agent.langgraph_state import CrawlAgentState, WorkflowCommands
from agent.ollama import parse_command_from_prompt
from utils.logger import logger
from utils.url_utils import normalize_url
from utils.validation_utils import is_valid_url
from config.settings import OLLAMA_MODEL, OLLAMA_URL
from crawler.statistics.tools import (
    get_crawl_statistics_summary,
    analyze_domain_performance,
    analyze_page_performance,
    analyze_time_based_trends,
    get_recent_crawl_activity,
    cleanup_old_statistics,
    preview_historical_import,
    import_historical_data
)
import re


# Initialize the LLM
# Convert the generate URL to base URL for LangChain
base_url = OLLAMA_URL.replace(
    "/api/generate", "") if "/api/generate" in OLLAMA_URL else OLLAMA_URL
llm = ChatOllama(
    model=OLLAMA_MODEL,
    base_url=base_url,
    temperature=0.0
)


async def intent_parser_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Parse user intent from natural language prompt.

    This node analyzes the user's prompt to determine what action they want
    to perform and extracts relevant parameters like URLs, file paths, and options.
    """
    logger.info("Parsing user intent...")

    user_prompt = state["user_prompt"]

    # Check for media flags in the original prompt
    screenshot = "--screenshot" in user_prompt.lower()
    pdf = "--pdf" in user_prompt.lower()

    # Clean the prompt for parsing
    clean_prompt = user_prompt.lower().replace(
        "--screenshot", "").replace("--pdf", "").strip()

    try:
        # First check for smart crawl keywords
        smart_crawl_keywords = [
            "smart crawl", "smart crawler", "schema crawl", "intelligent crawl"]
        is_smart_crawl_request = any(
            keyword in clean_prompt.lower() for keyword in smart_crawl_keywords)

        if is_smart_crawl_request:
            # Handle smart crawl requests
            # Extract URL from the prompt
            url = None
            for word in clean_prompt.split():
                if _looks_like_url(word):
                    url = word
                    break

            if url:
                url = normalize_url(url)
                if is_valid_url(url):
                    parsed_intent = {
                        "command": "smart_crawl",
                        "url": url
                    }
                    local_file_path = None
                    search_query = None
                else:
                    return {
                        **state,
                        "error": f"Invalid URL: {url}",
                        "next_action": WorkflowCommands.ERROR
                    }
            else:
                return {
                    **state,
                    "error": "No valid URL found for smart crawl",
                    "next_action": WorkflowCommands.ERROR
                }

        # Check for multiple URLs FIRST before specific request handling
        multiple_urls = _extract_multiple_urls(clean_prompt)
        logger.info(
            f"Multiple URLs detected: {multiple_urls} (count: {len(multiple_urls)})")

        if len(multiple_urls) > 1:
            # Handle multiple URL commands - check the original prompt, not parsed command
            prompt_lower = clean_prompt.lower()
            if any(cmd in prompt_lower for cmd in ["summarize", "summary"]):
                parsed_intent = {
                    "command": "crawl_multiple_websites",
                    "urls": multiple_urls,
                    "extract_links": False
                }
            elif any(cmd in prompt_lower for cmd in ["impressum", "imprint", "legal"]):
                parsed_intent = {
                    "command": "crawl_multiple_impressums",
                    "urls": multiple_urls
                }
            elif any(cmd in prompt_lower for cmd in ["extract links", "get links", "find links"]):
                parsed_intent = {
                    "command": "crawl_multiple_websites",
                    "urls": multiple_urls,
                    "extract_links": True
                }
            elif any(cmd in prompt_lower for cmd in ["sitemap", "extract sitemap", "get sitemap", "read sitemap"]):
                parsed_intent = {
                    "command": "sitemap_multiple",
                    "urls": multiple_urls
                }
            elif any(cmd in prompt_lower for cmd in ["download html", "save html", "get html", "html download"]):
                parsed_intent = {
                    "command": "download_multiple_html",
                    "urls": multiple_urls
                }
            else:
                # Default to multiple website crawling
                parsed_intent = {
                    "command": "crawl_multiple_websites",
                    "urls": multiple_urls,
                    "extract_links": False
                }

            url = None  # Clear single URL since we're using multiple
            local_file_path = None
            search_query = None

            logger.info(
                f"Intent parsing result - Command: {parsed_intent.get('command')}, URLs: {parsed_intent.get('urls')}")
            return {
                **state,
                "parsed_intent": parsed_intent,
                "url": url,
                "urls": parsed_intent.get("urls"),
                "local_file_path": local_file_path,
                "search_query": search_query,
                "next_action": _map_command_to_action(parsed_intent.get("command"), url, local_file_path, search_query)
            }

        # Check for HTML download keywords
        html_download_keywords = ["download html", "save html", "get html",
                                  "download page", "save page html", "html download"]
        is_html_download_request = any(
            keyword in clean_prompt.lower() for keyword in html_download_keywords)

        # Check for link extraction keywords
        link_extraction_keywords = ["extract links", "get links",
                                    "find links", "crawl and extract links", "links from"]
        is_link_extraction_request = any(
            keyword in clean_prompt.lower() for keyword in link_extraction_keywords)

        if is_html_download_request and not is_smart_crawl_request:
            # Handle HTML download requests
            # Extract URL from the prompt
            url = None
            for word in clean_prompt.split():
                if _looks_like_url(word):
                    url = word
                    break

            if url:
                url = normalize_url(url)
                if is_valid_url(url):
                    parsed_intent = {
                        "command": "download_html",
                        "url": url
                    }
                    local_file_path = None
                    search_query = None
                else:
                    return {
                        **state,
                        "error": f"Invalid URL: {url}",
                        "next_action": WorkflowCommands.ERROR
                    }
            else:
                return {
                    **state,
                    "error": "No valid URL found for HTML download",
                    "next_action": WorkflowCommands.ERROR
                }

        elif is_link_extraction_request and not is_smart_crawl_request and not is_html_download_request:
            # Handle link extraction requests
            # Extract URL from the prompt
            url = None
            for word in clean_prompt.split():
                if _looks_like_url(word):
                    url = word
                    break

            if url:
                url = normalize_url(url)
                if is_valid_url(url):
                    parsed_intent = {
                        "command": "crawl_with_links",
                        "url": url,
                        "extract_links": True
                    }
                    local_file_path = None
                    search_query = None
                else:
                    return {
                        **state,
                        "error": f"Invalid URL: {url}",
                        "next_action": WorkflowCommands.ERROR
                    }
            else:
                return {
                    **state,
                    "error": "No valid URL found for link extraction",
                    "next_action": WorkflowCommands.ERROR
                }

        # Check for search-related keywords (only if not smart crawl, link extraction, or HTML download)
        if not is_smart_crawl_request and not is_link_extraction_request and not is_html_download_request:
            search_keywords = ["search", "find", "look for",
                               "search for", "find information", "search and crawl"]
            is_search_request = any(keyword in clean_prompt.lower()
                                    for keyword in search_keywords)

            if is_search_request:
                # Handle search requests directly
                if "search and crawl" in clean_prompt.lower() or ("search" in clean_prompt.lower() and "crawl" in clean_prompt.lower()):
                    command = "search_and_crawl"
                else:
                    command = "search"

                # Extract search query by removing command keywords
                search_query = clean_prompt
                for keyword in search_keywords:
                    search_query = search_query.replace(keyword, "").strip()

                # Clean up common words more carefully
                words_to_remove = ["for", "about",
                                   "information", "data", "details"]
                for word in words_to_remove:
                    search_query = search_query.replace(f" {word} ", " ").replace(
                        f"{word} ", "").replace(f" {word}", "").strip()

                # Ensure we have a meaningful search query
                if not search_query or len(search_query) < 2:
                    search_query = clean_prompt

                parsed_intent = {
                    "command": command,
                    "query": search_query,
                    "url": None
                }
                url = None
                local_file_path = None
            else:
                # Use existing Ollama client for other command parsing (only if not already handled)
                if not (is_smart_crawl_request or is_html_download_request or is_link_extraction_request):
                    parsed_intent = await parse_command_from_prompt(clean_prompt)

                    # Extract URL, file path, or search query from the parsed intent
                    url = parsed_intent.get("url")
                    local_file_path = None
                    search_query = None

            # Check if it's a URL or local file path
            if url and not url.startswith(("http://", "https://")):
                # Check if it looks like a domain/URL (contains dots and common TLDs)
                if _looks_like_url(url):
                    # Treat as URL and normalize it
                    url = normalize_url(url)
                    if not is_valid_url(url):
                        return {
                            **state,
                            "error": f"Invalid URL: {url}",
                            "next_action": WorkflowCommands.ERROR
                        }
                else:
                    # Treat as local file path
                    local_file_path = url
                    url = None
            elif url:
                # Already has protocol, just normalize
                url = normalize_url(url)
                if not is_valid_url(url):
                    return {
                        **state,
                        "error": f"Invalid URL: {url}",
                        "next_action": WorkflowCommands.ERROR
                    }

        # Determine the next action based on parsed command
        command = parsed_intent.get("command", "chat")
        logger.info(
            f"Intent parsing result - Command: {command}, URLs: {parsed_intent.get('urls')}")
        next_action = _map_command_to_action(
            command, url, local_file_path, search_query)
        logger.info(f"Mapped to next action: {next_action}")

        # Extract URLs from parsed intent if available
        urls = parsed_intent.get("urls")

        # Use URL from parsed_intent if available, otherwise use the local url variable
        final_url = parsed_intent.get("url") or url

        return {
            **state,
            "parsed_intent": parsed_intent,
            "url": final_url,
            "urls": urls,
            "local_file_path": local_file_path,
            "search_query": search_query,
            "screenshot": screenshot,
            "pdf": pdf,
            "next_action": next_action
        }

    except Exception as e:
        logger.error(f"Error parsing intent: {e}")
        return {
            **state,
            "error": f"Failed to parse user intent: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def url_crawler_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Crawl a website URL and extract content.
    """
    logger.info(f"Crawling URL: {state['url']}")

    from agent.langgraph_tools import crawl_website_tool

    try:
        result = await crawl_website_tool.ainvoke({
            "url": state["url"],
            "screenshot": state["screenshot"],
            "pdf": state["pdf"]
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        # Determine next action based on parsed intent
        command = state["parsed_intent"].get("command", "")
        if command == "impressum":
            next_action = WorkflowCommands.EXTRACT_DATA
        elif command == "summarize":
            next_action = WorkflowCommands.GENERATE_SUMMARY
        else:
            next_action = WorkflowCommands.FORMAT_OUTPUT

        return {
            **state,
            "markdown_content": result["markdown_content"],
            "saved_html_path": result["saved_html_path"],
            "screenshot_path": result.get("screenshot_path"),
            "pdf_path": result.get("pdf_path"),
            "next_action": next_action
        }

    except Exception as e:
        logger.error(f"Error crawling URL: {e}")
        return {
            **state,
            "error": f"Failed to crawl URL: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def impressum_crawler_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Find and crawl impressum/legal notice page.
    """
    logger.info(f"Crawling impressum for: {state['url']}")

    from agent.langgraph_tools import crawl_impressum_tool

    try:
        result = await crawl_impressum_tool.ainvoke({"base_url": state["url"]})

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "markdown_content": result["markdown_content"],
            "saved_html_path": result["saved_html_path"],
            "next_action": WorkflowCommands.EXTRACT_DATA
        }

    except Exception as e:
        logger.error(f"Error crawling impressum: {e}")
        return {
            **state,
            "error": f"Failed to crawl impressum: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def local_file_processor_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Process a local HTML file.
    """
    logger.info(f"Processing local file: {state['local_file_path']}")

    from agent.langgraph_tools import process_local_file_tool

    try:
        result = await process_local_file_tool.ainvoke({"file_path": state["local_file_path"]})

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        # Determine next action based on parsed intent
        command = (state.get("parsed_intent") or {}).get("command", "")
        if "legal" in command or "impressum" in command:
            next_action = WorkflowCommands.EXTRACT_DATA
        elif "summarize" in command:
            next_action = WorkflowCommands.GENERATE_SUMMARY
        else:
            next_action = WorkflowCommands.FORMAT_OUTPUT

        return {
            **state,
            "markdown_content": result["markdown_content"],
            "next_action": next_action
        }

    except Exception as e:
        logger.error(f"Error processing local file: {e}")
        return {
            **state,
            "error": f"Failed to process local file: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def data_extractor_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Extract structured data from content (e.g., company data from impressum).
    """
    logger.info("Extracting structured data from content...")

    from agent.langgraph_tools import extract_company_data_tool, save_json_data_tool

    try:
        # Extract company data
        result = await extract_company_data_tool.ainvoke({
            "content": state["markdown_content"],
            "url": state["url"] or state["local_file_path"] or "unknown"
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        company_data = result["company_data"]

        # Save the extracted data
        save_result = save_json_data_tool.invoke({
            "data": company_data,
            "url": state["url"] or state["local_file_path"] or "unknown"
        })

        json_path = save_result.get(
            "json_path") if "error" not in save_result else None

        return {
            **state,
            "extracted_data": company_data,
            "company_data": company_data,
            "json_path": json_path,
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error extracting data: {e}")
        return {
            **state,
            "error": f"Failed to extract data: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def summarizer_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Generate a summary of the content.
    """
    logger.info("Generating summary of content...")

    from agent.langgraph_tools import generate_summary_tool, save_summary_tool

    try:
        # Generate summary
        result = await generate_summary_tool.ainvoke({
            "content": state["markdown_content"]
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        summary = result["summary"]

        # Save the summary
        save_result = save_summary_tool.invoke({
            "summary": summary,
            "url": state["url"] or state["local_file_path"] or "unknown"
        })

        summary_path = save_result.get(
            "summary_path") if "error" not in save_result else None

        return {
            **state,
            "summary": summary,
            "summary_path": summary_path,
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error generating summary: {e}")
        return {
            **state,
            "error": f"Failed to generate summary: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def result_formatter_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Format the final result for the user.
    """
    logger.info("Formatting final result...")
    logger.info(f"State keys available: {list(state.keys())}")
    if state.get("multiple_impressum_results"):
        logger.info(
            f"Found {len(state['multiple_impressum_results'])} impressum results")

    try:
        result = {}

        # Add content
        if state.get("summary"):
            result["summary"] = state["summary"]
        if state.get("company_data"):
            result["company_data"] = state["company_data"]
        if state.get("search_results"):
            result["search_results"] = state["search_results"]
        if state.get("crawled_search_results"):
            result["crawled_search_results"] = state["crawled_search_results"]

        # Add multiple URL results
        if state.get("multiple_crawl_results"):
            result["multiple_crawl_results"] = state["multiple_crawl_results"]
        if state.get("multiple_impressum_results"):
            result["multiple_impressum_results"] = state["multiple_impressum_results"]
        if state.get("html_download_results"):
            result["html_download_results"] = state["html_download_results"]

        # Add summary statistics
        if state.get("successful_summaries") is not None:
            result["successful_summaries"] = state["successful_summaries"]
        if state.get("failed_summaries") is not None:
            result["failed_summaries"] = state["failed_summaries"]

        # Add sitemap and statistics results
        if state.get("result") and isinstance(state["result"], dict):
            if state["result"].get("type") == "sitemap_extraction":
                result["sitemap_extraction"] = state["result"]
            elif state["result"].get("type") == "batch_sitemap_extraction":
                result["batch_sitemap_extraction"] = state["result"]
            elif state["result"].get("status") == "success" and "report" in state["result"]:
                # Handle statistics results
                result["statistics"] = state["result"]
            elif state["result"].get("status") == "success" and ("preview" in state["result"] or "results" in state["result"]):
                # Handle historical import results
                result["historical_import"] = state["result"]
            else:
                # Handle other generic results
                result.update(state["result"])

        # Check for specific command types
        parsed_intent = state.get("parsed_intent") or {}
        is_link_extraction = (
            parsed_intent.get("command") == "crawl_with_links" or
            state.get("next_action") == WorkflowCommands.CRAWL_WITH_LINKS or
            (parsed_intent and "extract_links" in parsed_intent)
        )
        is_html_download = (
            parsed_intent.get("command") in ["download_html", "download_multiple_html"] or
            state.get("next_action") in [
                WorkflowCommands.DOWNLOAD_HTML, WorkflowCommands.DOWNLOAD_MULTIPLE_HTML]
        )

        if is_link_extraction:
            if state.get("links"):
                result["links"] = state["links"]
            if state.get("link_summary"):
                result["link_summary"] = state["link_summary"]
            if state.get("page_title"):
                result["page_title"] = state["page_title"]

        # Add HTML download specific information
        if is_html_download:
            if state.get("page_title"):
                result["page_title"] = state["page_title"]
            if state.get("url"):
                result["downloaded_url"] = state["url"]
            if state.get("html_dir"):
                result["html_directory"] = state["html_dir"]
            # Add success message for HTML downloads
            if state.get("saved_html_path"):
                result["message"] = f"HTML content successfully downloaded and saved"

        if state.get("markdown_content") and not state.get("summary"):
            result["content"] = state["markdown_content"]

        # Add file paths
        files = {}
        if state.get("saved_html_path"):
            files["html"] = state["saved_html_path"]
        if state.get("screenshot_path"):
            files["screenshot"] = state["screenshot_path"]
        if state.get("pdf_path"):
            files["pdf"] = state["pdf_path"]
        if state.get("summary_path"):
            files["summary"] = state["summary_path"]
        if state.get("json_path"):
            files["json"] = state["json_path"]

        # Only include links file if this was a link extraction request
        if is_link_extraction and state.get("links_file_path"):
            files["links"] = state["links_file_path"]

        if files:
            result["files"] = files

        return {
            **state,
            "result": result,
            "next_action": WorkflowCommands.END
        }

    except Exception as e:
        logger.error(f"Error formatting result: {e}")
        return {
            **state,
            "error": f"Failed to format result: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def web_search_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Perform web search using the search query.
    """
    logger.info(f"Performing web search for: {state['search_query']}")

    from agent.langgraph_tools import web_search_tool

    try:
        result = web_search_tool.invoke({
            "query": state["search_query"],
            "max_results": 10
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "search_results": result["results"],
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error performing web search: {e}")
        return {
            **state,
            "error": f"Failed to perform web search: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def search_and_crawl_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Search the web and crawl the top results.
    """
    logger.info(f"Searching and crawling for: {state['search_query']}")

    from agent.langgraph_tools import search_and_crawl_tool

    try:
        result = await search_and_crawl_tool.ainvoke({
            "query": state["search_query"],
            "max_results": 5,
            "screenshot": state["screenshot"],
            "pdf": state["pdf"]
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "search_results": result["search_results"],
            "crawled_search_results": result["crawled_results"],
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error in search and crawl: {e}")
        return {
            **state,
            "error": f"Failed to search and crawl: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def crawl_with_links_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Crawl a website and extract internal/external links.
    """
    try:
        url = state["url"]
        logger.info(f"Crawling website with link extraction: {url}")

        from agent.langgraph_tools import crawl_with_links_tool

        # Extract screenshot and pdf options from user prompt
        screenshot = "--screenshot" in state.get("user_prompt", "")
        pdf = "--pdf" in state.get("user_prompt", "")

        result = await crawl_with_links_tool.ainvoke({
            "url": url,
            "screenshot": screenshot,
            "pdf": pdf,
            "extract_links": True,
            "save_links": True
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "url": result["url"],
            "page_title": result.get("page_title"),
            "markdown_content": result["markdown_content"],
            "saved_html_path": result["html_path"],
            "screenshot_path": result.get("screenshot_path"),
            "pdf_path": result.get("pdf_path"),
            "links": result.get("links"),
            "link_summary": result.get("link_summary"),
            "links_file_path": result.get("links_file_path"),
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error crawling with links: {e}")
        return {
            **state,
            "error": f"Failed to crawl with links: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def crawl_multiple_websites_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Crawl multiple websites and return their content.
    """
    try:
        urls = state["urls"]
        logger.info(f"Crawling multiple websites: {urls}")

        from agent.langgraph_tools import crawl_multiple_websites_tool

        # Extract options from parsed intent and user prompt
        screenshot = "--screenshot" in state.get("user_prompt", "")
        pdf = "--pdf" in state.get("user_prompt", "")
        extract_links = state.get("parsed_intent", {}).get(
            "extract_links", False)

        result = await crawl_multiple_websites_tool.ainvoke({
            "urls": urls,
            "screenshot": screenshot,
            "pdf": pdf,
            "extract_links": extract_links
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        # Check if this is a summarization request
        user_prompt = state.get("user_prompt", "").lower()
        is_summary_request = any(keyword in user_prompt for keyword in [
                                 "summarize", "summary", "summarise"])

        logger.info(
            f"User prompt: '{user_prompt}', is_summary_request: {is_summary_request}")

        if is_summary_request:
            # Generate summaries for each crawled website
            return {
                **state,
                "multiple_crawl_results": result["results"],
                "successful_crawls": result["successful_crawls"],
                "failed_crawls": result["failed_crawls"],
                "next_action": WorkflowCommands.GENERATE_MULTIPLE_SUMMARIES
            }
        else:
            # Regular crawling without summaries
            return {
                **state,
                "multiple_crawl_results": result["results"],
                "successful_crawls": result["successful_crawls"],
                "failed_crawls": result["failed_crawls"],
                "next_action": WorkflowCommands.FORMAT_OUTPUT
            }

    except Exception as e:
        logger.error(f"Error crawling multiple websites: {e}")
        return {
            **state,
            "error": f"Failed to crawl multiple websites: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def generate_multiple_summaries_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Generate summaries for multiple crawled websites.
    """
    try:
        multiple_crawl_results = state.get("multiple_crawl_results", [])
        if not multiple_crawl_results:
            return {
                **state,
                "error": "No crawled results found to summarize",
                "next_action": WorkflowCommands.ERROR
            }

        logger.info(
            f"Generating summaries for {len(multiple_crawl_results)} crawled websites")

        from agent.langgraph_tools import generate_summary_tool, save_summary_tool

        summarized_results = []
        successful_summaries = 0
        failed_summaries = 0

        for result in multiple_crawl_results:
            if not result.get("success") or not result.get("markdown_content"):
                # Skip failed crawls or empty content
                summarized_results.append({
                    **result,
                    "summary": None,
                    "summary_path": None,
                    "summary_error": "No content to summarize"
                })
                failed_summaries += 1
                continue

            try:
                # Generate summary for this website's content
                summary_result = await generate_summary_tool.ainvoke({
                    "content": result["markdown_content"]
                })

                if "error" in summary_result:
                    summarized_results.append({
                        **result,
                        "summary": None,
                        "summary_path": None,
                        "summary_error": summary_result["error"]
                    })
                    failed_summaries += 1
                    logger.warning(
                        f"Failed to generate summary for {result['url']}: {summary_result['error']}")
                    continue

                # Save the summary
                save_result = save_summary_tool.invoke({
                    "summary": summary_result["summary"],
                    "url": result["url"]
                })

                summarized_results.append({
                    **result,
                    "summary": summary_result["summary"],
                    "summary_path": save_result.get("summary_path"),
                    "summary_error": None
                })
                successful_summaries += 1
                logger.info(f"Generated summary for {result['url']}")

            except Exception as e:
                logger.error(
                    f"Error generating summary for {result['url']}: {e}")
                summarized_results.append({
                    **result,
                    "summary": None,
                    "summary_path": None,
                    "summary_error": f"Failed to generate summary: {str(e)}"
                })
                failed_summaries += 1

        logger.info(
            f"Summary generation completed. Success: {successful_summaries}/{len(multiple_crawl_results)}")

        return {
            **state,
            "multiple_crawl_results": summarized_results,
            "successful_summaries": successful_summaries,
            "failed_summaries": failed_summaries,
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error generating multiple summaries: {e}")
        return {
            **state,
            "error": f"Failed to generate summaries: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def crawl_multiple_impressums_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Crawl impressum pages from multiple websites.
    """
    try:
        urls = state["urls"]
        logger.info(f"Crawling multiple impressums: {urls}")

        from agent.langgraph_tools import crawl_multiple_impressums_tool

        result = await crawl_multiple_impressums_tool.ainvoke({
            "urls": urls
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "multiple_impressum_results": result["results"],
            "successful_crawls": result["successful_crawls"],
            "failed_crawls": result["failed_crawls"],
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error crawling multiple impressums: {e}")
        return {
            **state,
            "error": f"Failed to crawl multiple impressums: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def smart_crawler_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Perform smart crawling with schema learning.
    """
    try:
        url = state["url"]
        target_model = state.get("target_model")

        if not target_model:
            # Create a default generic data model for schema learning
            from pydantic import BaseModel, Field
            from typing import List, Dict, Any

            class GenericDataModel(BaseModel):
                """Generic model for extracting structured data from web pages."""
                title: str = Field(..., description="Main title or heading")
                content: str = Field(...,
                                     description="Main content or description")
                price: str = Field(
                    default="", description="Price information if available")
                link: str = Field(
                    default="", description="Link or URL if available")
                image: str = Field(
                    default="", description="Image URL if available")
                category: str = Field(
                    default="", description="Category or type if available")
                date: str = Field(
                    default="", description="Date information if available")
                author: str = Field(
                    default="", description="Author or source if available")

            target_model = GenericDataModel
            logger.info("Using default GenericDataModel for schema learning")

        logger.info(f"Smart crawling URL: {url}")

        from agent.langgraph_tools import smart_crawler_tool

        # Get schema learning parameters from state with proper defaults
        provider = state.get("schema_provider") or "ollama/qwen3"
        api_token = state.get("schema_api_token") or None
        force_learn = state.get("force_learn_schema") or False
        schema_dir = state.get("schema_dir") or "./learned_schemas"

        # Get the model schema
        target_model_schema = target_model.model_json_schema()

        result = await smart_crawler_tool.ainvoke({
            "url": url,
            "target_model_schema": target_model_schema,
            "provider": provider,
            "api_token": api_token,
            "force_learn": force_learn,
            "schema_dir": schema_dir
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "schema_extracted_data": result["extracted_data"],
            "learned_schema": result.get("schema_used"),
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error in smart crawler node: {e}")
        return {
            **state,
            "error": f"Failed to perform smart crawling: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def download_html_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Download HTML content from a single URL.
    """
    logger.info(f"Downloading HTML from: {state['url']}")

    from agent.langgraph_tools import download_html_tool

    try:
        result = await download_html_tool.ainvoke({
            "url": state["url"],
            "html_dir": "scraped_html",
            "wait_until": "networkidle",
            "page_timeout": 60000
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "saved_html_path": result["html_path"],
            "page_title": result["page_title"],
            "html_dir": result["html_dir"],
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error downloading HTML: {e}")
        return {
            **state,
            "error": f"Failed to download HTML: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def download_multiple_html_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Download HTML content from multiple URLs.
    """
    logger.info(f"Downloading HTML from multiple URLs: {state['urls']}")

    from agent.langgraph_tools import download_multiple_html_tool

    try:
        result = await download_multiple_html_tool.ainvoke({
            "urls": state["urls"],
            "html_dir": "scraped_html",
            "wait_until": "networkidle",
            "page_timeout": 60000
        })

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "html_download_results": result["results"],
            "successful_downloads": result["successful_downloads"],
            "failed_downloads": result["failed_downloads"],
            "html_dir": result["html_dir"],
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        logger.error(f"Error downloading HTML from multiple URLs: {e}")
        return {
            **state,
            "error": f"Failed to download HTML from multiple URLs: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


async def chat_responder_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Handle general chat requests that don't involve crawling.
    """
    logger.info("Handling chat request...")

    try:
        messages = [
            SystemMessage(
                content="You are a helpful assistant for web crawling and data extraction tasks."),
            HumanMessage(content=state["user_prompt"])
        ]

        response = await llm.ainvoke(messages)

        return {
            **state,
            "result": {"response": response.content},
            "next_action": WorkflowCommands.END
        }

    except Exception as e:
        logger.error(f"Error handling chat: {e}")
        return {
            **state,
            "error": f"Failed to handle chat request: {str(e)}",
            "next_action": WorkflowCommands.ERROR
        }


def _map_command_to_action(command: str, url: str, local_file_path: str, search_query: str = None) -> str:
    """Map parsed command to workflow action."""
    # Handle search-related commands
    if command in ["search", "web_search"]:
        return WorkflowCommands.WEB_SEARCH
    elif command in ["search_and_crawl"]:
        return WorkflowCommands.SEARCH_AND_CRAWL

    # Handle link extraction commands
    elif command in ["crawl_with_links"]:
        return WorkflowCommands.CRAWL_WITH_LINKS

    # Handle multiple URL commands
    elif command in ["crawl_multiple_websites"]:
        return WorkflowCommands.CRAWL_MULTIPLE_WEBSITES
    elif command in ["crawl_multiple_impressums"]:
        return WorkflowCommands.CRAWL_MULTIPLE_IMPRESSUMS

    # Handle smart crawl commands
    elif command in ["smart_crawl", "smart_crawler", "schema_crawl"]:
        return WorkflowCommands.SMART_CRAWL

    # Handle sitemap commands
    elif command in ["sitemap", "sitemap_extractor"]:
        return WorkflowCommands.SITEMAP_EXTRACTOR
    elif command in ["sitemap_multiple"]:
        return WorkflowCommands.SITEMAP_MULTIPLE

    # Handle HTML download commands
    elif command in ["download_html"]:
        return WorkflowCommands.DOWNLOAD_HTML
    elif command in ["download_multiple_html"]:
        return WorkflowCommands.DOWNLOAD_MULTIPLE_HTML

    # Handle impressum commands
    elif command == "impressum":
        if local_file_path:
            return WorkflowCommands.PROCESS_LOCAL_FILE
        else:
            return WorkflowCommands.CRAWL_IMPRESSUM

    # Handle summarize commands
    elif command == "summarize":
        if local_file_path:
            return WorkflowCommands.PROCESS_LOCAL_FILE
        else:
            return WorkflowCommands.CRAWL_URL

    # Handle statistics commands
    elif command == "statistics":
        return WorkflowCommands.STATISTICS_SUMMARY
    elif command == "domain_stats":
        return WorkflowCommands.DOMAIN_ANALYSIS
    elif command == "recent_activity":
        return WorkflowCommands.RECENT_ACTIVITY
    elif command == "preview_historical_import":
        return WorkflowCommands.PREVIEW_HISTORICAL_IMPORT
    elif command == "import_historical_data":
        return WorkflowCommands.IMPORT_HISTORICAL_DATA

    # Handle chat commands
    elif command == "chat":
        return WorkflowCommands.CHAT

    # Default routing logic
    else:
        # If we have a search query but no specific command, assume web search
        if search_query and not (url or local_file_path):
            return WorkflowCommands.WEB_SEARCH
        # Default to URL crawling if we have a URL
        elif url:
            return WorkflowCommands.CRAWL_URL
        # Default to local file processing if we have a file path
        elif local_file_path:
            return WorkflowCommands.PROCESS_LOCAL_FILE
        # Default to chat if nothing else matches
        else:
            return WorkflowCommands.CHAT


def _extract_multiple_urls(prompt: str) -> List[str]:
    """
    Extract multiple URLs from a prompt, handling comma-separated lists.

    Args:
        prompt: The user prompt to extract URLs from

    Returns:
        List of URLs found in the prompt
    """
    urls = []

    # Split by common separators and check each part
    parts = prompt.replace(',', ' ').replace(
        ';', ' ').replace('|', ' ').split()

    for part in parts:
        part = part.strip()
        if _looks_like_url(part):
            normalized_url = normalize_url(part)
            if is_valid_url(normalized_url):
                urls.append(normalized_url)

    return urls


def _looks_like_url(text: str) -> bool:
    """
    Check if a text string looks like a URL/domain name.

    Args:
        text: The text to check

    Returns:
        True if it looks like a URL, False otherwise
    """
    if not text:
        return False

    # Remove common prefixes that might be present
    text = text.strip().lower()
    if text.startswith(('www.', 'http://', 'https://')):
        return True

    # Check for domain-like patterns
    # Must contain at least one dot and have a valid TLD
    if '.' not in text:
        return False

    # Common TLDs and patterns
    common_tlds = [
        '.com', '.org', '.net', '.edu', '.gov', '.mil', '.int',
        '.de', '.uk', '.fr', '.it', '.es', '.nl', '.be', '.ch',
        '.at', '.se', '.no', '.dk', '.fi', '.pl', '.cz', '.hu',
        '.io', '.co', '.me', '.info', '.biz', '.name', '.pro'
    ]

    # Check if it ends with a common TLD
    for tld in common_tlds:
        if text.endswith(tld) or f'{tld}/' in text:
            return True

    # Check for domain pattern: word.word (at least)
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)+/?$'
    if re.match(domain_pattern, text):
        return True

    # Check for IP address pattern
    ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}(:\d+)?/?$'
    if re.match(ip_pattern, text):
        return True

    return False


async def sitemap_extractor_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Extract URLs from a website's sitemap.

    This node handles single sitemap extraction requests.
    """
    logger.info("Starting sitemap extraction...")

    url = state.get("url")
    if not url:
        error_msg = "No URL provided for sitemap extraction"
        logger.error(error_msg)
        return {
            **state,
            "error": error_msg,
            "next_action": WorkflowCommands.ERROR
        }

    try:
        # Import here to avoid circular imports
        from crawler.sitemap import read_sitemap

        # Extract sitemap
        result = await read_sitemap(url, timeout=30)

        if result['urls']:
            logger.info(
                f"Successfully extracted {len(result['urls'])} URLs from sitemap")

            # Format result for display
            formatted_result = {
                "type": "sitemap_extraction",
                "base_url": url,
                "sitemap_source": result['source'],
                "total_urls": len(result['urls']),
                "urls": result['urls'][:50],  # Limit display to first 50 URLs
                "truncated": len(result['urls']) > 50,
                "full_url_count": len(result['urls'])
            }

            return {
                **state,
                "result": formatted_result,
                "next_action": WorkflowCommands.FORMAT_OUTPUT
            }
        else:
            error_msg = f"No sitemap found for {url}"
            logger.warning(error_msg)
            return {
                **state,
                "error": error_msg,
                "next_action": WorkflowCommands.ERROR
            }

    except Exception as e:
        error_msg = f"Error extracting sitemap from {url}: {str(e)}"
        logger.error(error_msg)
        return {
            **state,
            "error": error_msg,
            "next_action": WorkflowCommands.ERROR
        }


async def sitemap_multiple_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Extract URLs from multiple websites' sitemaps concurrently.

    This node handles batch sitemap extraction requests.
    """
    logger.info("Starting batch sitemap extraction...")

    urls = state.get("urls")
    if not urls:
        error_msg = "No URLs provided for batch sitemap extraction"
        logger.error(error_msg)
        return {
            **state,
            "error": error_msg,
            "next_action": WorkflowCommands.ERROR
        }

    try:
        # Import here to avoid circular imports
        from crawler.sitemap import read_multiple_sitemaps

        # Extract sitemaps from all URLs
        results = await read_multiple_sitemaps(urls, timeout=30)

        # Process results
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        total_urls = sum(len(r['urls']) for r in successful_results)

        logger.info(
            f"Batch sitemap extraction completed: {len(successful_results)}/{len(urls)} successful")

        # Format result for display
        formatted_result = {
            "type": "batch_sitemap_extraction",
            "total_websites": len(urls),
            "successful_extractions": len(successful_results),
            "failed_extractions": len(failed_results),
            "total_urls_found": total_urls,
            "results": results
        }

        return {
            **state,
            "result": formatted_result,
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        error_msg = f"Error in batch sitemap extraction: {str(e)}"
        logger.error(error_msg)
        return {
            **state,
            "error": error_msg,
            "next_action": WorkflowCommands.ERROR
        }


async def statistics_summary_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Generate comprehensive crawl statistics summary.

    This node generates a detailed report of crawl statistics including
    domain performance, success rates, and trends.
    """
    logger.info("Generating crawl statistics summary...")

    try:
        # Extract parameters from user prompt or use defaults
        user_prompt = state.get("user_prompt", "").lower()

        # Parse days_back from prompt (default 30)
        days_back = 30
        if "last" in user_prompt:
            import re
            days_match = re.search(r'last\s+(\d+)\s+days?', user_prompt)
            if days_match:
                days_back = int(days_match.group(1))

        # Parse top_n from prompt (default 10)
        top_n = 10
        if "top" in user_prompt:
            top_match = re.search(r'top\s+(\d+)', user_prompt)
            if top_match:
                top_n = int(top_match.group(1))

        # Generate statistics report
        input_data = {"input_data": {"days_back": days_back, "top_n": top_n}}
        result = get_crawl_statistics_summary.invoke(input_data)

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "result": result,
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        error_msg = f"Error generating statistics summary: {str(e)}"
        logger.error(error_msg)
        return {
            **state,
            "error": error_msg,
            "next_action": WorkflowCommands.ERROR
        }


async def domain_analysis_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Analyze performance metrics for a specific domain.

    This node provides detailed analysis of crawl performance for a domain
    including success rates, timing trends, and error patterns.
    """
    logger.info("Analyzing domain performance...")

    try:
        # Extract domain from state or user prompt
        domain = state.get("domain")
        if not domain:
            # Try to extract from user prompt
            user_prompt = state.get("user_prompt", "")
            import re
            domain_match = re.search(
                r'(?:domain|site)\s+([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', user_prompt)
            if domain_match:
                domain = domain_match.group(1)
            else:
                return {
                    **state,
                    "error": "No domain specified for analysis",
                    "next_action": WorkflowCommands.ERROR
                }

        # Analyze domain performance
        input_data = {"input_data": {"domain": domain}}
        result = analyze_domain_performance.invoke(input_data)

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "result": result,
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        error_msg = f"Error analyzing domain performance: {str(e)}"
        logger.error(error_msg)
        return {
            **state,
            "error": error_msg,
            "next_action": WorkflowCommands.ERROR
        }


async def recent_activity_node(state: CrawlAgentState) -> CrawlAgentState:
    """
    Get recent crawl activity.

    This node retrieves and displays recent crawl operations
    with their status and performance metrics.
    """
    logger.info("Retrieving recent crawl activity...")

    try:
        # Extract limit from user prompt or use default
        user_prompt = state.get("user_prompt", "").lower()
        limit = 20

        if "last" in user_prompt:
            import re
            limit_match = re.search(r'last\s+(\d+)', user_prompt)
            if limit_match:
                limit = int(limit_match.group(1))

        # Get recent activity
        result = get_recent_crawl_activity.invoke({"limit": limit})

        if "error" in result:
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "result": result,
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        error_msg = f"Error retrieving recent activity: {str(e)}"
        logger.error(error_msg)
        return {
            **state,
            "error": error_msg,
            "next_action": WorkflowCommands.ERROR
        }


def preview_historical_import_node(state: CrawlAgentState) -> CrawlAgentState:
    """Preview what historical data would be imported."""
    logger.info("Generating historical import preview...")

    try:
        # Preview historical import (use default project root)
        input_data = {"input_data": {"dry_run": True, "project_root": None}}
        result = preview_historical_import.invoke(input_data)

        if result.get("error"):
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "result": result,
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        error_msg = f"Error generating import preview: {str(e)}"
        logger.error(error_msg)
        return {
            **state,
            "error": error_msg,
            "next_action": WorkflowCommands.ERROR
        }


def import_historical_data_node(state: CrawlAgentState) -> CrawlAgentState:
    """Import historical crawl data."""
    logger.info("Importing historical crawl data...")

    try:
        # Import historical data (use default project root, default to actual import)
        dry_run = state.get("dry_run", False)
        input_data = {"input_data": {"dry_run": dry_run, "project_root": None}}
        result = import_historical_data.invoke(input_data)

        if result.get("error"):
            return {
                **state,
                "error": result["error"],
                "next_action": WorkflowCommands.ERROR
            }

        return {
            **state,
            "result": result,
            "next_action": WorkflowCommands.FORMAT_OUTPUT
        }

    except Exception as e:
        error_msg = f"Error importing historical data: {str(e)}"
        logger.error(error_msg)
        return {
            **state,
            "error": error_msg,
            "next_action": WorkflowCommands.ERROR
        }
