"""
LangGraph Workflow for the Crawl4AI Agent

This module defines the main StateGraph that orchestrates the crawling
and data extraction workflow using LangGraph.
"""

import async<PERSON>
from typing import Literal
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from agent.langgraph_state import CrawlAgentState, WorkflowCommands, create_initial_state
from agent.langgraph_nodes import (
    intent_parser_node,
    url_crawler_node,
    impressum_crawler_node,
    local_file_processor_node,
    data_extractor_node,
    summarizer_node,
    web_search_node,
    search_and_crawl_node,
    crawl_with_links_node,
    crawl_multiple_websites_node,
    crawl_multiple_impressums_node,
    smart_crawler_node,
    sitemap_extractor_node,
    sitemap_multiple_node,
    download_html_node,
    download_multiple_html_node,
    result_formatter_node,
    chat_responder_node,
    statistics_summary_node,
    domain_analysis_node,
    recent_activity_node,
    preview_historical_import_node,
    import_historical_data_node
)
from utils.logger import logger


def create_workflow() -> StateGraph:
    """
    Create and configure the LangGraph workflow for the Crawl4AI agent.

    Returns:
        Configured StateGraph ready for execution
    """
    # Create the state graph
    workflow = StateGraph(CrawlAgentState)

    # Add nodes
    workflow.add_node(WorkflowCommands.INTENT_PARSER, intent_parser_node)
    workflow.add_node(WorkflowCommands.URL_CRAWLER, url_crawler_node)
    workflow.add_node(WorkflowCommands.IMPRESSUM_FINDER,
                      impressum_crawler_node)
    workflow.add_node(WorkflowCommands.LOCAL_FILE_PROCESSOR,
                      local_file_processor_node)
    workflow.add_node(WorkflowCommands.DATA_EXTRACTOR, data_extractor_node)
    workflow.add_node(WorkflowCommands.SUMMARIZER, summarizer_node)
    workflow.add_node(WorkflowCommands.WEB_SEARCHER, web_search_node)
    workflow.add_node(WorkflowCommands.SEARCH_AND_CRAWLER,
                      search_and_crawl_node)
    workflow.add_node(WorkflowCommands.LINK_EXTRACTOR, crawl_with_links_node)
    workflow.add_node(WorkflowCommands.CRAWL_MULTIPLE_WEBSITES,
                      crawl_multiple_websites_node)
    workflow.add_node(WorkflowCommands.CRAWL_MULTIPLE_IMPRESSUMS,
                      crawl_multiple_impressums_node)
    workflow.add_node(WorkflowCommands.SMART_CRAWLER, smart_crawler_node)
    workflow.add_node(WorkflowCommands.SITEMAP_EXTRACTOR,
                      sitemap_extractor_node)
    workflow.add_node(WorkflowCommands.SITEMAP_MULTIPLE, sitemap_multiple_node)
    workflow.add_node(WorkflowCommands.DOWNLOAD_HTML, download_html_node)
    workflow.add_node(WorkflowCommands.DOWNLOAD_MULTIPLE_HTML,
                      download_multiple_html_node)
    workflow.add_node(WorkflowCommands.RESULT_FORMATTER, result_formatter_node)
    workflow.add_node(WorkflowCommands.CHAT_RESPONDER, chat_responder_node)

    # Statistics nodes
    workflow.add_node(WorkflowCommands.STATISTICS_SUMMARY,
                      statistics_summary_node)
    workflow.add_node(WorkflowCommands.DOMAIN_ANALYSIS, domain_analysis_node)
    workflow.add_node(WorkflowCommands.RECENT_ACTIVITY, recent_activity_node)
    workflow.add_node(WorkflowCommands.PREVIEW_HISTORICAL_IMPORT,
                      preview_historical_import_node)
    workflow.add_node(WorkflowCommands.IMPORT_HISTORICAL_DATA,
                      import_historical_data_node)

    # Set entry point
    workflow.set_entry_point(WorkflowCommands.INTENT_PARSER)

    # Add conditional edges from intent parser
    workflow.add_conditional_edges(
        WorkflowCommands.INTENT_PARSER,
        _route_after_intent_parsing,
        {
            WorkflowCommands.CRAWL_URL: WorkflowCommands.URL_CRAWLER,
            WorkflowCommands.CRAWL_IMPRESSUM: WorkflowCommands.IMPRESSUM_FINDER,
            WorkflowCommands.PROCESS_LOCAL_FILE: WorkflowCommands.LOCAL_FILE_PROCESSOR,
            WorkflowCommands.WEB_SEARCH: WorkflowCommands.WEB_SEARCHER,
            WorkflowCommands.SEARCH_AND_CRAWL: WorkflowCommands.SEARCH_AND_CRAWLER,
            WorkflowCommands.CRAWL_WITH_LINKS: WorkflowCommands.LINK_EXTRACTOR,
            WorkflowCommands.CRAWL_MULTIPLE_WEBSITES: WorkflowCommands.CRAWL_MULTIPLE_WEBSITES,
            WorkflowCommands.CRAWL_MULTIPLE_IMPRESSUMS: WorkflowCommands.CRAWL_MULTIPLE_IMPRESSUMS,
            WorkflowCommands.SMART_CRAWL: WorkflowCommands.SMART_CRAWLER,
            WorkflowCommands.SITEMAP_EXTRACTOR: WorkflowCommands.SITEMAP_EXTRACTOR,
            WorkflowCommands.SITEMAP_MULTIPLE: WorkflowCommands.SITEMAP_MULTIPLE,
            WorkflowCommands.DOWNLOAD_HTML: WorkflowCommands.DOWNLOAD_HTML,
            WorkflowCommands.DOWNLOAD_MULTIPLE_HTML: WorkflowCommands.DOWNLOAD_MULTIPLE_HTML,
            WorkflowCommands.STATISTICS_SUMMARY: WorkflowCommands.STATISTICS_SUMMARY,
            WorkflowCommands.DOMAIN_ANALYSIS: WorkflowCommands.DOMAIN_ANALYSIS,
            WorkflowCommands.RECENT_ACTIVITY: WorkflowCommands.RECENT_ACTIVITY,
            WorkflowCommands.PREVIEW_HISTORICAL_IMPORT: WorkflowCommands.PREVIEW_HISTORICAL_IMPORT,
            WorkflowCommands.IMPORT_HISTORICAL_DATA: WorkflowCommands.IMPORT_HISTORICAL_DATA,
            WorkflowCommands.CHAT: WorkflowCommands.CHAT_RESPONDER,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add conditional edges from URL crawler
    workflow.add_conditional_edges(
        WorkflowCommands.URL_CRAWLER,
        _route_after_crawling,
        {
            WorkflowCommands.EXTRACT_DATA: WorkflowCommands.DATA_EXTRACTOR,
            WorkflowCommands.GENERATE_SUMMARY: WorkflowCommands.SUMMARIZER,
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add edges from impressum crawler
    workflow.add_conditional_edges(
        WorkflowCommands.IMPRESSUM_FINDER,
        _route_after_crawling,
        {
            WorkflowCommands.EXTRACT_DATA: WorkflowCommands.DATA_EXTRACTOR,
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add conditional edges from local file processor
    workflow.add_conditional_edges(
        WorkflowCommands.LOCAL_FILE_PROCESSOR,
        _route_after_crawling,
        {
            WorkflowCommands.EXTRACT_DATA: WorkflowCommands.DATA_EXTRACTOR,
            WorkflowCommands.GENERATE_SUMMARY: WorkflowCommands.SUMMARIZER,
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add edges from data extractor and summarizer to result formatter
    workflow.add_conditional_edges(
        WorkflowCommands.DATA_EXTRACTOR,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.SUMMARIZER,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add edges to END
    workflow.add_conditional_edges(
        WorkflowCommands.RESULT_FORMATTER,
        _route_to_end,
        {
            WorkflowCommands.END: END,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add edges from search nodes to result formatter
    workflow.add_conditional_edges(
        WorkflowCommands.WEB_SEARCHER,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.SEARCH_AND_CRAWLER,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add edges from link extractor to result formatter
    workflow.add_conditional_edges(
        WorkflowCommands.LINK_EXTRACTOR,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add edges from multiple URL nodes
    workflow.add_conditional_edges(
        WorkflowCommands.CRAWL_MULTIPLE_WEBSITES,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.CRAWL_MULTIPLE_IMPRESSUMS,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.SMART_CRAWLER,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.SITEMAP_EXTRACTOR,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.SITEMAP_MULTIPLE,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add edges from HTML download nodes
    workflow.add_conditional_edges(
        WorkflowCommands.DOWNLOAD_HTML,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.DOWNLOAD_MULTIPLE_HTML,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.CHAT_RESPONDER,
        _route_to_end,
        {
            WorkflowCommands.END: END,
            WorkflowCommands.ERROR: END,
        }
    )

    # Add edges from statistics nodes to result formatter
    workflow.add_conditional_edges(
        WorkflowCommands.STATISTICS_SUMMARY,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.DOMAIN_ANALYSIS,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.RECENT_ACTIVITY,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.PREVIEW_HISTORICAL_IMPORT,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    workflow.add_conditional_edges(
        WorkflowCommands.IMPORT_HISTORICAL_DATA,
        _route_to_end,
        {
            WorkflowCommands.FORMAT_OUTPUT: WorkflowCommands.RESULT_FORMATTER,
            WorkflowCommands.ERROR: END,
        }
    )

    return workflow


def _route_after_intent_parsing(state: CrawlAgentState) -> Literal[
    "crawl_url", "crawl_impressum", "process_local_file", "web_search", "search_and_crawl", "crawl_with_links", "crawl_multiple_websites", "crawl_multiple_impressums", "smart_crawl", "download_html", "download_multiple_html", "sitemap_extractor", "sitemap_multiple", "statistics_summary", "domain_analysis", "recent_activity", "preview_historical_import", "import_historical_data", "chat", "__error__"
]:
    """Route after intent parsing based on next_action."""
    next_action = state.get("next_action")

    if state.get("error"):
        return WorkflowCommands.ERROR

    if next_action == WorkflowCommands.CRAWL_URL:
        return WorkflowCommands.CRAWL_URL
    elif next_action == WorkflowCommands.CRAWL_IMPRESSUM:
        return WorkflowCommands.CRAWL_IMPRESSUM
    elif next_action == WorkflowCommands.PROCESS_LOCAL_FILE:
        return WorkflowCommands.PROCESS_LOCAL_FILE
    elif next_action == WorkflowCommands.WEB_SEARCH:
        return WorkflowCommands.WEB_SEARCH
    elif next_action == WorkflowCommands.SEARCH_AND_CRAWL:
        return WorkflowCommands.SEARCH_AND_CRAWL
    elif next_action == WorkflowCommands.CRAWL_WITH_LINKS:
        return WorkflowCommands.CRAWL_WITH_LINKS
    elif next_action == WorkflowCommands.CRAWL_MULTIPLE_WEBSITES:
        return WorkflowCommands.CRAWL_MULTIPLE_WEBSITES
    elif next_action == WorkflowCommands.CRAWL_MULTIPLE_IMPRESSUMS:
        return WorkflowCommands.CRAWL_MULTIPLE_IMPRESSUMS
    elif next_action == WorkflowCommands.SMART_CRAWL:
        return WorkflowCommands.SMART_CRAWL
    elif next_action == WorkflowCommands.SITEMAP_EXTRACTOR:
        return WorkflowCommands.SITEMAP_EXTRACTOR
    elif next_action == WorkflowCommands.SITEMAP_MULTIPLE:
        return WorkflowCommands.SITEMAP_MULTIPLE
    elif next_action == WorkflowCommands.DOWNLOAD_HTML:
        return WorkflowCommands.DOWNLOAD_HTML
    elif next_action == WorkflowCommands.DOWNLOAD_MULTIPLE_HTML:
        return WorkflowCommands.DOWNLOAD_MULTIPLE_HTML
    elif next_action == WorkflowCommands.STATISTICS_SUMMARY:
        return WorkflowCommands.STATISTICS_SUMMARY
    elif next_action == WorkflowCommands.DOMAIN_ANALYSIS:
        return WorkflowCommands.DOMAIN_ANALYSIS
    elif next_action == WorkflowCommands.RECENT_ACTIVITY:
        return WorkflowCommands.RECENT_ACTIVITY
    elif next_action == WorkflowCommands.PREVIEW_HISTORICAL_IMPORT:
        return WorkflowCommands.PREVIEW_HISTORICAL_IMPORT
    elif next_action == WorkflowCommands.IMPORT_HISTORICAL_DATA:
        return WorkflowCommands.IMPORT_HISTORICAL_DATA
    elif next_action == WorkflowCommands.CHAT:
        return WorkflowCommands.CHAT
    else:
        return WorkflowCommands.ERROR


def _route_after_crawling(state: CrawlAgentState) -> Literal[
    "extract_data", "generate_summary", "format_output", "__error__"
]:
    """Route after crawling based on next_action."""
    next_action = state.get("next_action")

    if state.get("error"):
        return WorkflowCommands.ERROR

    if next_action == WorkflowCommands.EXTRACT_DATA:
        return WorkflowCommands.EXTRACT_DATA
    elif next_action == WorkflowCommands.GENERATE_SUMMARY:
        return WorkflowCommands.GENERATE_SUMMARY
    elif next_action == WorkflowCommands.FORMAT_OUTPUT:
        return WorkflowCommands.FORMAT_OUTPUT
    else:
        return WorkflowCommands.ERROR


def _route_to_end(state: CrawlAgentState) -> Literal["format_output", "__end__", "__error__"]:
    """Route to end based on next_action."""
    next_action = state.get("next_action")

    if state.get("error"):
        return WorkflowCommands.ERROR

    if next_action == WorkflowCommands.FORMAT_OUTPUT:
        return WorkflowCommands.FORMAT_OUTPUT
    elif next_action == WorkflowCommands.END:
        return WorkflowCommands.END
    else:
        return WorkflowCommands.ERROR


class CrawlAgent:
    """
    Main agent class that wraps the LangGraph workflow.
    """

    def __init__(self):
        """Initialize the agent with the workflow."""
        self.workflow = create_workflow()
        # Add memory for conversation persistence
        memory = MemorySaver()
        self.app = self.workflow.compile(checkpointer=memory)
        logger.info("Crawl4AI LangGraph agent initialized")

    async def process_request(self, user_prompt: str, thread_id: str = "default") -> dict:
        """
        Process a user request through the LangGraph workflow.

        Args:
            user_prompt: The user's natural language request
            thread_id: Thread ID for conversation persistence

        Returns:
            Dictionary containing the result or error
        """
        try:
            logger.info(f"Processing request: {user_prompt}")

            # Validate input
            if not user_prompt or not user_prompt.strip():
                return {"error": "Empty request provided"}

            # Create initial state
            initial_state = create_initial_state(user_prompt.strip())

            # Configure thread
            config = {"configurable": {"thread_id": thread_id}}

            # Run the workflow with timeout
            try:
                final_state = await asyncio.wait_for(
                    self.app.ainvoke(initial_state, config),
                    timeout=300  # 5 minute timeout
                )
            except asyncio.TimeoutError:
                logger.error("Workflow execution timed out")
                return {"error": "Request timed out. Please try again with a simpler request."}

            # Log final state for debugging
            logger.info(f"Final state keys: {list(final_state.keys())}")
            if "result" in final_state:
                logger.info(
                    f"Result keys: {list(final_state['result'].keys()) if final_state['result'] else 'Result is None'}")
            else:
                logger.info("No 'result' key in final state")

            # Return result or error
            if final_state.get("error"):
                logger.error(f"Workflow error: {final_state['error']}")
                return {"error": final_state["error"]}
            else:
                result = final_state.get(
                    "result", {"message": "Request processed successfully"})
                logger.info("Request processed successfully")
                return result

        except Exception as e:
            logger.error(f"Error processing request: {e}", exc_info=True)
            return {"error": f"Failed to process request: {str(e)}"}

    def get_workflow_graph(self):
        """Get the workflow graph for visualization."""
        return self.app.get_graph()
