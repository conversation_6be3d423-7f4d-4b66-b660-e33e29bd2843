#!/usr/bin/env python3
"""
Test script for LangGraph HTML download functionality.
"""

import asyncio
from agent.langgraph_workflow import create_workflow
from agent.langgraph_state import create_initial_state


async def test_single_html_download():
    """Test single HTML download through LangGraph."""
    print("Testing single HTML download...")
    
    # Create workflow
    workflow = create_workflow()
    app = workflow.compile()
    
    # Test single HTML download
    initial_state = create_initial_state("download html from example.com")
    
    result = await app.ainvoke(initial_state)
    
    print("Result:", result.get("result", "No result"))
    if result.get("error"):
        print("Error:", result["error"])
    else:
        print("HTML Path:", result.get("html_path"))
        print("Page Title:", result.get("page_title"))


async def test_multiple_html_download():
    """Test multiple HTML download through LangGraph."""
    print("\nTesting multiple HTML download...")
    
    # Create workflow
    workflow = create_workflow()
    app = workflow.compile()
    
    # Test multiple HTML download
    initial_state = create_initial_state("download html from example.com,httpbin.org/html")
    
    result = await app.ainvoke(initial_state)
    
    print("Result:", result.get("result", "No result"))
    if result.get("error"):
        print("Error:", result["error"])
    else:
        print("Download Results:", result.get("html_download_results"))
        print("Successful Downloads:", result.get("successful_downloads"))


async def main():
    """Run HTML download tests."""
    print("🚀 Testing LangGraph HTML Download Functionality")
    
    try:
        await test_single_html_download()
        await test_multiple_html_download()
        
        print("\n✅ All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
