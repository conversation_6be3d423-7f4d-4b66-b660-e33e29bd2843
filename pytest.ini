[tool:pytest]
# Pytest configuration for Crawl4AI Agent tests

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Async support
asyncio_mode = auto

# Output options
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes
    --disable-warnings

# Filter warnings from external dependencies
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:crawl4ai.*

# Markers for test categorization
markers =
    unit: Unit tests for individual functions
    integration: Integration tests for complete workflows
    tools: Tests for LangGraph tools
    slow: Tests that take a long time to run
    network: Tests that require network access
    ollama: Tests that require Ollama to be running

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (when using pytest-cov)
[coverage:run]
source = crawler, agent
omit = 
    */tests/*
    */venv/*
    */examples/*
    setup.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
