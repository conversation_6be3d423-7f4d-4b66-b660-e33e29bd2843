"""
Core crawling functionality for basic website crawling operations.

This module contains the fundamental crawling functions that handle:
- Single website crawling with optional screenshots, PDFs, and link extraction
- Multiple website batch processing
- Dedicated HTML downloading and storage
- Local HTML file processing
- Page title extraction utilities
"""

import os
import base64
from typing import Dict, List, Union, Optional, Any
from crawl4ai import AsyncWebCrawler, CacheMode, BrowserConfig, CrawlerRunConfig

from utils.file_utils import save_html_to_dir, read_local_file, save_media_to_dir
from utils.logger import logger
from .utils import _extract_and_categorize_links, _extract_page_title
from .statistics import track_crawl, CrawlType


@track_crawl(crawl_type=CrawlType.WEBSITE)
async def crawl_website(
    url: str,
    screenshot: bool = False,
    pdf: bool = False,
    extract_links: bool = False,
    save_html: bool = True,
    html_dir: str = "scraped_html"
) -> tuple[
    str, str | None, str | None, str | None, Dict[str,
                                                  List[str]] | None, str | None
]:
    """
    Crawl a website, save its raw HTML, and return its content in markdown format.
    Optionally capture screenshots, PDFs, and extract internal/external links.

    Args:
        url (str): The URL of the website to crawl
        screenshot (bool): Whether to capture a screenshot
        pdf (bool): Whether to generate a PDF
        extract_links (bool): Whether to extract and categorize internal/external links
        save_html (bool): Whether to save the raw HTML content to disk (default: True)
        html_dir (str): Directory to save HTML files in (default: "scraped_html")

    Returns:
        A tuple containing:
        - Markdown content
        - Path to saved HTML file (if save_html=True)
        - Path to saved screenshot (if requested)
        - Path to saved PDF (if requested)
        - Dictionary with 'internal' and 'external' link lists (if extract_links=True)
        - Page title (if available)
    """
    # Configure browser to run in headless mode (without GUI)
    browser_config = BrowserConfig(headless=True)
    # Configure crawler to bypass cache for fresh results and capture media if requested
    # Magic Mode simulates human-like browsing to avoid bot detection
    crawler_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        screenshot=screenshot,
        pdf=pdf,
        magic=True,  # Simplifies a lot of interaction, simulates human-like browsing
        # Simulate user interactions (mouse movements) to avoid bot detection
        simulate_user=True,
        # Wait for the page to be fully loaded before proceeding
        wait_until="networkidle",  # Wait for network to be idle
        page_timeout=60000,  # 60 second timeout
        delay_before_return_html=0.5  # Small delay to ensure page is stable
    )

    # Create and use crawler within context manager for proper resource cleanup
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(url=url, config=crawler_config)

        if not result.success:
            logger.error(f"Failed to crawl {url}: {result.error_message}")
            return (
                f"Error: Could not crawl website content for {url}.",
                None,
                None,
                None,
                None,
                None,
            )

        # Save the fetched HTML content if requested
        saved_html_path = None
        if save_html:
            saved_html_path = save_html_to_dir(result.html, url, html_dir)
            logger.info(f"HTML content saved to: {saved_html_path}")

        # Handle screenshot if requested
        screenshot_path = None
        if screenshot and result.screenshot:
            screenshot_path = save_media_to_dir(
                base64.b64decode(result.screenshot), url, "screenshots", "png"
            )
            logger.info(f"Screenshot saved to: {screenshot_path}")

        # Handle PDF if requested
        pdf_path = None
        if pdf and result.pdf:
            pdf_path = save_media_to_dir(result.pdf, url, "pdfs", "pdf")
            logger.info(f"PDF saved to: {pdf_path}")

        # Extract page title
        page_title = _extract_page_title(result)

        # Extract and categorize links if requested
        links_data = None
        if extract_links and result.links:
            links_data = _extract_and_categorize_links(url, result.links)
            logger.info(
                f"Extracted {len(links_data['internal'])} internal and {len(links_data['external'])} external links"
            )

        return (
            result.markdown,
            saved_html_path,
            screenshot_path,
            pdf_path,
            links_data,
            page_title,
        )


@track_crawl(crawl_type=CrawlType.WEBSITE, extract_url_from_args=False)
async def crawl_multiple_websites(
    urls: Union[str, List[str]],
    screenshot: bool = False,
    pdf: bool = False,
    extract_links: bool = False,
    save_html: bool = True,
    html_dir: str = "scraped_html",
) -> List[Dict[str, any]]:
    """
    Crawl multiple websites and return their content.

    Args:
        urls: Single URL string or list of URLs to crawl
        screenshot: Whether to capture screenshots
        pdf: Whether to generate PDFs
        extract_links: Whether to extract and categorize links
        save_html: Whether to save the raw HTML content to disk (default: True)
        html_dir: Directory to save HTML files in (default: "scraped_html")

    Returns:
        List of dictionaries containing crawl results for each URL
    """
    # Normalize input to list
    if isinstance(urls, str):
        url_list = [urls]
    else:
        url_list = urls

    results = []

    for i, url in enumerate(url_list, 1):
        logger.info(f"Crawling URL {i}/{len(url_list)}: {url}")

        try:
            # Crawl individual website
            result = await crawl_website(url, screenshot, pdf, extract_links, save_html, html_dir)
            (
                markdown_content,
                html_path,
                screenshot_path,
                pdf_path,
                links_data,
                page_title,
            ) = result

            # Create result dictionary
            crawl_result = {
                "url": url,
                "success": True,
                "page_title": page_title,
                "markdown_content": markdown_content,
                "html_path": html_path,
                "screenshot_path": screenshot_path,
                "pdf_path": pdf_path,
                "links_data": links_data,
                "error": None,
            }

            results.append(crawl_result)
            logger.info(f"Successfully crawled {url}")

        except Exception as e:
            logger.error(f"Failed to crawl {url}: {e}")
            error_result = {
                "url": url,
                "success": False,
                "page_title": None,
                "markdown_content": None,
                "html_path": None,
                "screenshot_path": None,
                "pdf_path": None,
                "links_data": None,
                "error": str(e),
            }
            results.append(error_result)

    logger.info(
        f"Completed crawling {len(url_list)} URLs. Success: {sum(1 for r in results if r['success'])}/{len(url_list)}"
    )
    return results


@track_crawl(crawl_type=CrawlType.LOCAL_FILE, extract_url_from_args=False)
async def crawl_local_file(local_file_path: str) -> str | None:
    """
    Crawls a local HTML file and returns its content in Markdown format.

    Args:
        local_file_path (str): The path (absolute or relative) to the local HTML file.

    Returns:
        The Markdown content of the file, or None if crawling fails.
    """
    html_content = read_local_file(local_file_path)
    if html_content is None:
        return None

    file_url = f"file://{os.path.abspath(local_file_path)}"
    # Use a specific config for local files, bypassing cache is a good default.
    # Explicitly disable console capture to work around a bug in crawl4ai.
    # WORKAROUND: The library has a bug causing an UnboundLocalError when
    # console capture is False. Setting it to True avoids the error.
    # Note: file_url might not strictly be needed, as html_content is provided.
    config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS, capture_console_messages=True
    )
    # Configure a headless browser, as crawl4ai might need it for parsing.
    # browser_config = BrowserConfig(headless=True)

    async with AsyncWebCrawler() as crawler:
        # Provide the HTML content directly for more reliable processing.
        result = await crawler.arun(
            url=file_url, html_content=html_content, config=config
        )
        if result.success:
            logger.info(f"Successfully crawled local file: {local_file_path}")
            return result.markdown
        else:
            logger.error(
                f"Failed to crawl {local_file_path}: {result.error_message}")
            return None


@track_crawl(crawl_type=CrawlType.WEBSITE)
async def download_html(
    url: str,
    html_dir: str = "scraped_html",
    wait_until: str = "networkidle",
    page_timeout: int = 60000
) -> tuple[str | None, str | None]:
    """
    Download and save HTML content from a website with minimal processing.
    This function focuses specifically on HTML downloading without markdown conversion.

    Args:
        url (str): The URL of the website to download HTML from
        html_dir (str): Directory to save HTML files in (default: "scraped_html")
        wait_until (str): When to consider page loading complete (default: "networkidle")
        page_timeout (int): Page load timeout in milliseconds (default: 60000)

    Returns:
        A tuple containing:
        - Path to saved HTML file (if successful)
        - Page title (if available)
    """
    # Configure browser to run in headless mode (without GUI)
    browser_config = BrowserConfig(headless=True)
    # Configure crawler for HTML download with minimal processing
    crawler_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        magic=True,  # Simplifies interaction, simulates human-like browsing
        simulate_user=True,  # Simulate user interactions to avoid bot detection
        wait_until=wait_until,  # Wait for the page to be fully loaded
        page_timeout=page_timeout,
        delay_before_return_html=0.5  # Small delay to ensure page is stable
    )

    # Create and use crawler within context manager for proper resource cleanup
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(url=url, config=crawler_config)

        if not result.success:
            logger.error(
                f"Failed to download HTML from {url}: {result.error_message}")
            return None, None

        # Save the fetched HTML content
        saved_html_path = save_html_to_dir(result.html, url, html_dir)
        logger.info(f"HTML content downloaded and saved to: {saved_html_path}")

        # Extract page title
        page_title = _extract_page_title(result)

        return saved_html_path, page_title


@track_crawl(crawl_type=CrawlType.WEBSITE, extract_url_from_args=False)
async def download_multiple_html(
    urls: Union[str, List[str]],
    html_dir: str = "scraped_html",
    wait_until: str = "networkidle",
    page_timeout: int = 60000
) -> List[Dict[str, any]]:
    """
    Download and save HTML content from multiple websites.
    This function focuses specifically on HTML downloading without markdown conversion.

    Args:
        urls: Single URL string or list of URLs to download HTML from
        html_dir: Directory to save HTML files in (default: "scraped_html")
        wait_until: When to consider page loading complete (default: "networkidle")
        page_timeout: Page load timeout in milliseconds (default: 60000)

    Returns:
        List of dictionaries containing download results for each URL
    """
    # Normalize input to list
    if isinstance(urls, str):
        url_list = [urls]
    else:
        url_list = urls

    results = []

    for i, url in enumerate(url_list, 1):
        logger.info(f"Downloading HTML {i}/{len(url_list)}: {url}")

        try:
            # Download HTML from individual website
            html_path, page_title = await download_html(url, html_dir, wait_until, page_timeout)

            # Create result dictionary
            download_result = {
                "url": url,
                "success": html_path is not None,
                "page_title": page_title,
                "html_path": html_path,
                "error": None,
            }

            results.append(download_result)

            if html_path:
                logger.info(f"Successfully downloaded HTML from {url}")
            else:
                logger.warning(f"Failed to download HTML from {url}")

        except Exception as e:
            logger.error(f"Failed to download HTML from {url}: {e}")
            error_result = {
                "url": url,
                "success": False,
                "page_title": None,
                "html_path": None,
                "error": str(e),
            }
            results.append(error_result)

    logger.info(
        f"Completed HTML download for {len(url_list)} URLs. Success: {sum(1 for r in results if r['success'])}/{len(url_list)}"
    )
    return results
