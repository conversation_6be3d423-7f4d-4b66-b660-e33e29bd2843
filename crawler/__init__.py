__version__ = "0.6.0"

# Import from modular structure
from .core import (
    crawl_website,
    crawl_multiple_websites,
    crawl_local_file,
    download_html,
    download_multiple_html
)
from .company_data import (
    crawl_impressum,
    crawl_multiple_impressums,
    extract_company_data,
    extract_multiple_company_data
)
from .smart import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    smart_crawler
)
from .sitemap import (
    read_sitemap,
    read_multiple_sitemaps
)
from .utils import (
    save_links_to_file,
    _extract_and_categorize_links,
    _extract_page_title
)

# Maintain backward compatibility by exposing all functions
__all__ = [
    # Core crawling functions
    "crawl_website",
    "crawl_multiple_websites",
    "crawl_local_file",

    # HTML download functions
    "download_html",
    "download_multiple_html",

    # Impressum crawling functions
    "crawl_impressum",
    "crawl_multiple_impressums",
    "extract_company_data",
    "extract_multiple_company_data",

    # Smart crawling functions
    "SchemaLearner",
    "smart_crawler",

    # Sitemap crawling functions
    "read_sitemap",
    "read_multiple_sitemaps",

    # Utility functions
    "save_links_to_file",
    "_extract_and_categorize_links",
    "_extract_page_title"
]
